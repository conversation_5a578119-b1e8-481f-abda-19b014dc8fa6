﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="E:\source\dronesplanning\src\algorithm\allocator\task_allocator.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\algorithm\path_planner\ipath_planner.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\algorithm\path_planner\rrtstar_planner.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\config.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\core\base_object.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\core\entity_object.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\core\entity_state.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\core\geometry\ishape.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\core\geometry\shape_factory.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\core\geometry\shapes\box_shape.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\core\geometry\shapes\capsule_shape.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\core\geometry\shapes\compound_shape.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\core\geometry\shapes\cone_shape.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\core\geometry\shapes\convex_hull_shape.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\core\geometry\shapes\cylinder_shape.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\core\geometry\shapes\ellipsoid_shape.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\core\geometry\shapes\line_shape.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\core\geometry\shapes\mesh_shape.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\core\geometry\shapes\plane_shape.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\core\geometry\shapes\point_shape.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\core\geometry\shapes\polygon_shape.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\core\geometry\shapes\sphere_shape.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\core\movement_strategy.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\environment\collision\collision_engine.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\environment\coordinate\coordinate_manager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\environment\coordinate\task_space.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\environment\entities\obstacle.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\environment\entities\zone.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\environment\environment.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\environment\indices\bvh_spatial_index.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\environment\maps\igridmap.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\environment\maps\single_gridmap.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\environment\maps\tiled_gridmap.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\main.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\mission\mission.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\mission\task.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\params\param_defs.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\params\param_json.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\params\parameters.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\params\paramregistry.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\planning\itask_planner.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\planning\mission_planner.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\planning\planning_result.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\planning\planning_types.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\planning\task_planners\surveymultipoints_taskplanner.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\planning\task_planners\surveysphere_taskplanner.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\uav\dynamics\fixedwing_dynamics.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\uav\dynamics\multirotor_dynamics.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\uav\dynamics\vtol_dynamics.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\uav\energies\fixedwing_energies.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\uav\energies\multirotor_energies.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\uav\energies\vtol_energies.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\uav\flight_strategy.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\uav\idynamic_model.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\uav\ienergy_model.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\uav\uav.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\uav\uav_config.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\utils\enum_utils.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\utils\file_utils.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\utils\logging.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\utils\object_id.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\source\dronesplanning\src\utils\orientation_utils.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\utils\coordinate_converter.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\utils\geometry_manager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="E:\source\dronesplanning\include\algorithm\algorithm_object.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\algorithm\allocator\itask_allocator.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\algorithm\allocator\task_allocator.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\algorithm\evaluator\energy_evaluator.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\algorithm\evaluator\itrajectory_evaluator.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\algorithm\path_planner\ipath_planner.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\algorithm\path_planner\rrtstar_planner.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\algorithm\trajectory_optimizer\itrajectory_optimizer.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\algorithm\trajectory_optimizer\trajectory_optimizer.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\config.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\core\base_object.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\core\entity_object.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\core\entity_state.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\core\geometry\ishape.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\core\geometry\shape_factory.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\core\geometry\shapes\box_shape.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\core\geometry\shapes\capsule_shape.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\core\geometry\shapes\compound_shape.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\core\geometry\shapes\cone_shape.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\core\geometry\shapes\convex_hull_shape.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\core\geometry\shapes\cylinder_shape.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\core\geometry\shapes\ellipsoid_shape.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\core\geometry\shapes\line_shape.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\core\geometry\shapes\mesh_shape.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\core\geometry\shapes\plane_shape.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\core\geometry\shapes\point_shape.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\core\geometry\shapes\polygon_shape.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\core\geometry\shapes\sphere_shape.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\core\movement_strategy.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\core\types.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\drones.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\environment\collision\collision_engine.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\environment\collision\collision_types.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\environment\collision\detector_registry.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\environment\coordinate\coordinate_manager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\environment\coordinate\task_space.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\environment\entities\obstacle.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\environment\entities\zone.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\environment\environment.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\environment\environment_fwd.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\environment\indices\attribute_index.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\environment\indices\bvh_spatial_index.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\environment\indices\ispatial_index.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\environment\maps\igridmap.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\environment\maps\single_gridmap.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\environment\maps\tiled_gridmap.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\environment\storage\object_storage.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\mission\capability_requirement.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\mission\control_point.h">
      <Filter>Header Files</Filter>
    </ClInclude>

    <ClInclude Include="E:\source\dronesplanning\include\mission\mission.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\mission\mission_fwd.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\mission\task.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\mission\task_fwd.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\mission\task_params.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\mission\task_strategies.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\mission\task_targets.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\params\param_defs.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\params\param_json.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\params\parameters.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\params\paramregistry.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\planning\itask_planner.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\planning\mission_planner.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\planning\planning_result.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\planning\planning_types.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\planning\task_planners\followpath_taskplanner.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\planning\task_planners\loiterpoint_taskplanner.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\planning\task_planners\scanarea_taskplanner.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\planning\task_planners\surveycylinder_taskplanner.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\planning\task_planners\surveymultipoints_taskplanner.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\planning\task_planners\surveysphere_taskplanner.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\uav\dynamics\fixedwing_dynamics.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\uav\dynamics\multirotor_dynamics.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\uav\dynamics\vtol_dynamics.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\uav\energies\fixedwing_energies.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\uav\energies\multirotor_energies.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\uav\energies\vtol_energies.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\uav\flight_strategy.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\uav\idynamic_model.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\uav\ienergy_model.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\uav\uav.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\uav\uav_config.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\uav\uav_fwd.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\uav\uav_types.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\utils\enum_utils.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\utils\file_utils.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\utils\logging.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\utils\object_id.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\utils\orientation_utils.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\utils\stopwatch.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\source\dronesplanning\include\utils\thread_safe_cache.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\utils\coordinate_converter.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\utils\geometry_manager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="E:\source\dronesplanning\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="E:\source\dronesplanning\include\core\base_object.tpp" />
    <None Include="E:\source\dronesplanning\include\environment\storage\object_storage.tpp" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Header Files">
      <UniqueIdentifier>{7F80B4AC-8E94-3F42-8C35-2C69CF2CCB79}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{BD6BD8DC-0190-37B9-8A10-FD2E6D700532}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>