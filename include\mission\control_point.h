// include/mission/control_point.h
#pragma once

#include "core/types.h"
#include "params/parameters.h"
#include "utils/logging.h"
#include "utils/geometry_manager.h"
#include <vector>
#include <memory>
#include <string>
#include <optional>
#include <map>
#include <algorithm>
#include <functional>
#include <chrono>

namespace NSDrones {
	namespace NSMission {
		using namespace NSDrones::NSUtils;
		using namespace NSDrones::NSCore;
		using namespace NSDrones::NSParams;

		/**
		 * @struct PayloadActionCommand
		 * @brief 定义一个载荷动作命令
		 *
		 * 载荷动作命令用于在控制点执行特定的载荷操作，如拍照、投放、扫描等。
		 * 支持参数化配置，可以适应不同类型的载荷设备。
		 */
		struct PayloadActionCommand {
			std::string payload_id = "";     ///< 目标载荷 ID (可选，空表示默认载荷)
			std::string command_name = "";   ///< 命令名称 (例如 "TakePhoto", "SetZoom", "Drop")
			std::map<std::string, ParamValue> parameters; ///< 命令参数 (键值对)

			// --- 执行控制 ---
			double delay_before_seconds = 0.0;  ///< 到达控制点后延迟执行时间（秒）
			double timeout_seconds = 30.0;      ///< 命令执行超时时间（秒）
			bool is_critical = false;           ///< 是否为关键命令（失败时是否中止任务）

			// --- 构造函数 ---
			PayloadActionCommand() = default;

			/**
			 * @brief 构造函数
			 * @param cmd 命令名称
			 * @param params 命令参数
			 * @param p_id 载荷ID
			 * @param delay 延迟执行时间
			 * @param timeout 超时时间
			 * @param critical 是否为关键命令
			 */
			PayloadActionCommand(std::string cmd,
								std::map<std::string, ParamValue> params = {},
								std::string p_id = "",
								double delay = 0.0,
								double timeout = 30.0,
								bool critical = false)
				: payload_id(std::move(p_id)),
				  command_name(std::move(cmd)),
				  parameters(std::move(params)),
				  delay_before_seconds(delay),
				  timeout_seconds(timeout),
				  is_critical(critical) {}

			// --- 验证方法 ---
			/**
			 * @brief 验证命令的有效性
			 * @return true表示命令有效
			 */
			bool isValid() const {
				return !command_name.empty() &&
					   delay_before_seconds >= 0.0 &&
					   timeout_seconds > 0.0;
			}

			/**
			 * @brief 获取命令的描述信息
			 * @return 命令描述字符串
			 */
			std::string getDescription() const {
				std::string desc = command_name;
				if (!payload_id.empty()) {
					desc += " (载荷: " + payload_id + ")";
				}
				if (delay_before_seconds > 0.0) {
					desc += " [延迟: " + std::to_string(delay_before_seconds) + "s]";
				}
				return desc;
			}

			// --- 比较操作符 ---
			bool operator==(const PayloadActionCommand& other) const {
				return payload_id == other.payload_id &&
					   command_name == other.command_name &&
					   parameters == other.parameters &&
					   std::abs(delay_before_seconds - other.delay_before_seconds) < 1e-6 &&
					   std::abs(timeout_seconds - other.timeout_seconds) < 1e-6 &&
					   is_critical == other.is_critical;
			}

			bool operator!=(const PayloadActionCommand& other) const {
				return !(*this == other);
			}
		};


		/**
		 * @class PayloadAction
		 * @brief 载荷动作的抽象基类
		 *
		 * 定义了载荷动作的通用接口，具体的动作（如拍照、投放、扫描）需要从此类派生。
		 * 支持命令模式，可以将动作转换为具体的执行命令。
		 *
		 * @note 由于序列化的复杂性，建议在任务定义中使用PayloadActionCommand
		 *       而不是直接使用PayloadAction的派生类。
		 */
		class PayloadAction {
		public:
			virtual ~PayloadAction() = default;

			/**
			 * @brief 获取动作的描述性字符串
			 * @return 动作描述
			 */
			virtual std::string getDescription() const = 0;

			/**
			 * @brief 获取动作类型名称
			 * @return 动作类型字符串
			 */
			virtual std::string getActionType() const = 0;

			/**
			 * @brief 获取此动作对应的执行命令
			 * @return 如果动作可以转换为命令则返回命令，否则返回nullopt
			 */
			virtual std::optional<PayloadActionCommand> getCommand() const {
				return std::nullopt;
			}

			/**
			 * @brief 验证动作的有效性
			 * @return true表示动作有效
			 */
			virtual bool isValid() const = 0;

			/**
			 * @brief 估算动作执行时间
			 * @return 预计执行时间（秒）
			 */
			virtual double getEstimatedDuration() const {
				return 1.0; // 默认1秒
			}

			/**
			 * @brief 检查动作是否需要特定的载荷类型
			 * @param payload_type 载荷类型
			 * @return true表示需要该类型载荷
			 */
			virtual bool requiresPayloadType(const std::string& payload_type) const {
				return false; // 默认不需要特定载荷
			}
		};

		// 定义载荷动作的智能指针类型别名
		using PayloadActionPtr = std::shared_ptr<PayloadAction>;

		// --- 具体的载荷动作实现 ---

		/**
		 * @class PhotoAction
		 * @brief 拍照动作实现
		 */
		class PhotoAction : public PayloadAction {
		private:
			std::string camera_id_;
			int photo_count_;
			double interval_seconds_;
			std::map<std::string, ParamValue> camera_settings_;

		public:
			PhotoAction(std::string camera_id = "", int count = 1, double interval = 0.0)
				: camera_id_(std::move(camera_id)), photo_count_(count), interval_seconds_(interval) {}

			std::string getDescription() const override {
				return "拍照动作 (数量: " + std::to_string(photo_count_) +
					   ", 间隔: " + std::to_string(interval_seconds_) + "s)";
			}

			std::string getActionType() const override { return "Photo"; }

			bool isValid() const override {
				return photo_count_ > 0 && interval_seconds_ >= 0.0;
			}

			double getEstimatedDuration() const override {
				return photo_count_ * interval_seconds_ + 2.0; // 额外2秒准备时间
			}

			std::optional<PayloadActionCommand> getCommand() const override {
				std::map<std::string, ParamValue> params;
				params["count"] = photo_count_;
				params["interval"] = interval_seconds_;
				for (const auto& [key, value] : camera_settings_) {
					params[key] = value;
				}
				return PayloadActionCommand("TakePhoto", params, camera_id_);
			}

			bool requiresPayloadType(const std::string& payload_type) const override {
				return payload_type == "Camera" || payload_type == "Gimbal";
			}

			// 设置方法
			PhotoAction& setPhotoCount(int count) { photo_count_ = count; return *this; }
			PhotoAction& setInterval(double interval) { interval_seconds_ = interval; return *this; }
			PhotoAction& addCameraSetting(const std::string& key, const ParamValue& value) {
				camera_settings_[key] = value; return *this;
			}
		};

		/**
		 * @class DropAction
		 * @brief 投放动作实现
		 */
		class DropAction : public PayloadAction {
		private:
			std::string dropper_id_;
			int drop_count_;
			double drop_interval_;
			std::string drop_type_;

		public:
			DropAction(std::string dropper_id = "", int count = 1,
					  double interval = 0.0, std::string type = "")
				: dropper_id_(std::move(dropper_id)), drop_count_(count),
				  drop_interval_(interval), drop_type_(std::move(type)) {}

			std::string getDescription() const override {
				return "投放动作 (类型: " + drop_type_ + ", 数量: " + std::to_string(drop_count_) + ")";
			}

			std::string getActionType() const override { return "Drop"; }

			bool isValid() const override {
				return drop_count_ > 0 && drop_interval_ >= 0.0;
			}

			double getEstimatedDuration() const override {
				return drop_count_ * drop_interval_ + 1.0;
			}

			std::optional<PayloadActionCommand> getCommand() const override {
				std::map<std::string, ParamValue> params;
				params["count"] = drop_count_;
				params["interval"] = drop_interval_;
				if (!drop_type_.empty()) {
					params["type"] = drop_type_;
				}
				return PayloadActionCommand("Drop", params, dropper_id_, 0.0, 10.0, true);
			}

			bool requiresPayloadType(const std::string& payload_type) const override {
				return payload_type == "Dropper" || payload_type == "Dispenser";
			}
		};

		/**
		 * @class ScanAction
		 * @brief 扫描动作实现
		 */
		class ScanAction : public PayloadAction {
		private:
			std::string scanner_id_;
			double scan_duration_;
			std::string scan_mode_;
			double scan_resolution_;

		public:
			ScanAction(std::string scanner_id = "", double duration = 5.0,
					  std::string mode = "normal", double resolution = 1.0)
				: scanner_id_(std::move(scanner_id)), scan_duration_(duration),
				  scan_mode_(std::move(mode)), scan_resolution_(resolution) {}

			std::string getDescription() const override {
				return "扫描动作 (模式: " + scan_mode_ + ", 时长: " +
					   std::to_string(scan_duration_) + "s)";
			}

			std::string getActionType() const override { return "Scan"; }

			bool isValid() const override {
				return scan_duration_ > 0.0 && scan_resolution_ > 0.0;
			}

			double getEstimatedDuration() const override {
				return scan_duration_ + 1.0; // 额外1秒启动时间
			}

			std::optional<PayloadActionCommand> getCommand() const override {
				std::map<std::string, ParamValue> params;
				params["duration"] = scan_duration_;
				params["mode"] = scan_mode_;
				params["resolution"] = scan_resolution_;
				return PayloadActionCommand("StartScan", params, scanner_id_);
			}

			bool requiresPayloadType(const std::string& payload_type) const override {
				return payload_type == "LiDAR" || payload_type == "Scanner" ||
					   payload_type == "Radar";
			}
		};

		/**
		 * @struct ControlPoint
		 * @brief 定义一个任务控制点及其相关约束和要求
		 *
		 * 控制点是任务执行过程中的关键位置，可以是航路点、盘旋中心、区域顶点等。
		 * 每个控制点可以包含位置、高度、速度、姿态、停留时间和载荷动作等约束。
		 *
		 * ## 坐标系说明
		 * - position: 使用 WGS84 坐标系（经度/纬度/高度）
		 * - 这是外部接口标准，内部计算时会转换为 ECEF 或 NED 局部坐标
		 *
		 * ## 使用场景
		 * - 航路点飞行：定义必须通过的路径点
		 * - 盘旋任务：定义盘旋中心和约束
		 * - 区域扫描：定义扫描区域的边界点
		 * - 载荷操作：定义执行载荷动作的位置
		 */
		struct ControlPoint {
			// --- 基本位置信息 ---
			WGS84Point position;                            ///< 控制点目标位置 (WGS84坐标系)
			ControlPointType type = ControlPointType::WAYPOINT_MUST_PASS; ///< 控制点类型

			// --- 高度要求 ---
			double required_altitude = 100.0;              ///< 要求的高度值 (米)
			AltitudeType height_type = AltitudeType::ABOVE_GROUND_LEVEL; ///< 高度类型

			// --- 速度约束 (可选) ---
			std::optional<Vector3D> required_speed = std::nullopt; ///< 期望速度向量 (m/s, ENU坐标系)
			std::optional<double> max_speed = std::nullopt;         ///< 最大允许速度 (m/s)
			std::optional<double> min_speed = std::nullopt;         ///< 最小允许速度 (m/s)

			// --- 姿态约束 (可选) ---
			std::optional<double> required_heading_deg = std::nullopt; ///< 要求航向角 (度, 0-360, 相对正北)
			std::optional<Orientation> required_attitude = std::nullopt; ///< 要求姿态 (四元数)
			std::optional<double> heading_tolerance_deg = std::nullopt;  ///< 航向容差 (度)

			// --- 时间约束 ---
			Time required_loiter_time = 0.0;               ///< 停留时间 (秒)
			std::optional<Time> arrival_time = std::nullopt; ///< 期望到达时间 (秒，相对任务开始)
			std::optional<Time> departure_time = std::nullopt; ///< 期望离开时间 (秒，相对任务开始)

			// --- 载荷动作 ---
			PayloadActionPtr action = nullptr;              ///< 载荷动作指针
			std::vector<PayloadActionCommand> action_commands; ///< 载荷动作命令列表

			// --- 约束和容差 ---
			std::optional<double> position_tolerance = std::nullopt; ///< 位置容差 (米)
			std::optional<double> altitude_tolerance = std::nullopt; ///< 高度容差 (米)
			bool is_mandatory = true;                       ///< 是否为强制通过点

			// --- 元数据 ---
			std::string name = "";                          ///< 控制点名称
			std::string description = "";                   ///< 控制点描述
			std::map<std::string, std::string> metadata;   ///< 额外元数据

			// --- 构造函数 ---
			ControlPoint() = default;

			/**
			 * @brief 基本构造函数
			 * @param pos 控制点WGS84坐标位置
			 * @param cp_type 控制点类型
			 * @param alt 要求的高度值
			 * @param ht 高度类型
			 * @param point_name 控制点名称
			 */
			ControlPoint(const WGS84Point& pos,
						ControlPointType cp_type = ControlPointType::WAYPOINT_MUST_PASS,
						double alt = 100.0,
						AltitudeType ht = AltitudeType::ABOVE_GROUND_LEVEL,
						std::string point_name = "")
				: position(pos), type(cp_type), required_altitude(alt),
				  height_type(ht), name(std::move(point_name)) {}

			/**
			 * @brief 便利构造函数（从经纬度构造）
			 * @param lat 纬度（度）
			 * @param lon 经度（度）
			 * @param alt_msl 海拔高度（米）
			 * @param cp_type 控制点类型
			 * @param point_name 控制点名称
			 */
			ControlPoint(double lat, double lon, double alt_msl,
						ControlPointType cp_type = ControlPointType::WAYPOINT_MUST_PASS,
						std::string point_name = "")
				: position(lon, lat, alt_msl), type(cp_type),
				  required_altitude(alt_msl), height_type(AltitudeType::ABSOLUTE_ALTITUDE),
				  name(std::move(point_name)) {}

			// --- 链式设置方法 (支持流式配置) ---

			// 速度设置
			ControlPoint& setRequiredSpeed(const Vector3D& speed) {
				required_speed = speed; return *this;
			}
			ControlPoint& setSpeedLimits(double min_spd, double max_spd) {
				min_speed = min_spd; max_speed = max_spd; return *this;
			}
			ControlPoint& clearSpeedRequirements() {
				required_speed.reset(); min_speed.reset(); max_speed.reset(); return *this;
			}

			// 姿态设置
			ControlPoint& setRequiredHeading(double heading_deg, double tolerance_deg = 5.0) {
				required_heading_deg = heading_deg;
				heading_tolerance_deg = tolerance_deg;
				return *this;
			}
			ControlPoint& setRequiredAttitude(const Orientation& attitude) {
				required_attitude = attitude; return *this;
			}
			ControlPoint& clearAttitudeRequirements() {
				required_heading_deg.reset(); required_attitude.reset();
				heading_tolerance_deg.reset(); return *this;
			}

			// 时间设置
			ControlPoint& setLoiterTime(Time duration) {
				required_loiter_time = std::max(0.0, duration); return *this;
			}
			ControlPoint& setArrivalTime(Time arrival) {
				arrival_time = arrival; return *this;
			}
			ControlPoint& setDepartureTime(Time departure) {
				departure_time = departure; return *this;
			}
			ControlPoint& clearTimeRequirements() {
				arrival_time.reset(); departure_time.reset(); return *this;
			}

			// 载荷动作设置
			ControlPoint& setPayloadAction(PayloadActionPtr act) {
				action = std::move(act); return *this;
			}
			ControlPoint& addActionCommand(const PayloadActionCommand& cmd) {
				action_commands.push_back(cmd); return *this;
			}
			ControlPoint& clearPayloadActions() {
				action = nullptr; action_commands.clear(); return *this;
			}

			// 约束设置
			ControlPoint& setPositionTolerance(double tolerance) {
				position_tolerance = tolerance; return *this;
			}
			ControlPoint& setAltitudeTolerance(double tolerance) {
				altitude_tolerance = tolerance; return *this;
			}
			ControlPoint& setMandatory(bool mandatory) {
				is_mandatory = mandatory; return *this;
			}

			// 元数据设置
			ControlPoint& setName(const std::string& point_name) {
				name = point_name; return *this;
			}
			ControlPoint& setDescription(const std::string& desc) {
				description = desc; return *this;
			}
			ControlPoint& addMetadata(const std::string& key, const std::string& value) {
				metadata[key] = value; return *this;
			}

			// --- 状态检查方法 ---

			// 速度相关检查
			bool hasSpeedRequirement() const { return required_speed.has_value(); }
			bool hasSpeedLimits() const { return min_speed.has_value() || max_speed.has_value(); }
			bool hasAnySpeedConstraint() const { return hasSpeedRequirement() || hasSpeedLimits(); }

			// 姿态相关检查
			bool hasHeadingRequirement() const { return required_heading_deg.has_value(); }
			bool hasAttitudeRequirement() const { return required_attitude.has_value(); }
			bool hasAnyAttitudeConstraint() const { return hasHeadingRequirement() || hasAttitudeRequirement(); }

			// 时间相关检查
			bool hasLoiterTime() const { return required_loiter_time > Constants::TIME_EPSILON; }
			bool hasArrivalTime() const { return arrival_time.has_value(); }
			bool hasDepartureTime() const { return departure_time.has_value(); }
			bool hasAnyTimeConstraint() const { return hasLoiterTime() || hasArrivalTime() || hasDepartureTime(); }

			// 载荷动作检查
			bool hasPayloadAction() const { return action != nullptr; }
			bool hasActionCommands() const { return !action_commands.empty(); }
			bool hasAnyPayloadAction() const { return hasPayloadAction() || hasActionCommands(); }

			// 约束检查
			bool hasPositionTolerance() const { return position_tolerance.has_value(); }
			bool hasAltitudeTolerance() const { return altitude_tolerance.has_value(); }
			bool hasAnyTolerance() const { return hasPositionTolerance() || hasAltitudeTolerance(); }

			// 元数据检查
			bool hasName() const { return !name.empty(); }
			bool hasDescription() const { return !description.empty(); }
			bool hasMetadata() const { return !metadata.empty(); }

			// --- 值获取方法 ---
			Vector3D getRequiredSpeedOrZero() const {
				return required_speed.value_or(Vector3D::Zero());
			}

			double getPositionToleranceOrDefault(double default_val = 1.0) const {
				return position_tolerance.value_or(default_val);
			}

			double getAltitudeToleranceOrDefault(double default_val = 2.0) const {
				return altitude_tolerance.value_or(default_val);
			}

			double getHeadingToleranceOrDefault(double default_val = 5.0) const {
				return heading_tolerance_deg.value_or(default_val);
			}

			// --- 验证方法 ---
			/**
			 * @brief 验证控制点的有效性
			 * @return true表示控制点有效
			 */
			bool isValid() const {
				// 检查位置有效性
				if (!position.isValid()) {
					return false;
				}

				// 检查高度合理性
				if (!std::isfinite(required_altitude)) {
					return false;
				}

				// 检查时间约束合理性
				if (required_loiter_time < 0.0) {
					return false;
				}

				if (arrival_time.has_value() && departure_time.has_value()) {
					if (departure_time.value() <= arrival_time.value()) {
						return false; // 离开时间必须晚于到达时间
					}
				}

				// 检查速度约束合理性
				if (min_speed.has_value() && max_speed.has_value()) {
					if (min_speed.value() > max_speed.value()) {
						return false;
					}
				}

				// 检查载荷动作有效性
				if (action && !action->isValid()) {
					return false;
				}

				for (const auto& cmd : action_commands) {
					if (!cmd.isValid()) {
						return false;
					}
				}

				return true;
			}

			/**
			 * @brief 获取控制点的详细描述
			 * @return 包含所有约束信息的描述字符串
			 */
			std::string getDetailedDescription() const {
				std::string desc = name.empty() ? "控制点" : name;
				desc += " @ " + position.toString();

				if (hasAnySpeedConstraint()) {
					desc += " [速度约束]";
				}
				if (hasAnyAttitudeConstraint()) {
					desc += " [姿态约束]";
				}
				if (hasAnyTimeConstraint()) {
					desc += " [时间约束]";
				}
				if (hasAnyPayloadAction()) {
					desc += " [载荷动作]";
				}

				return desc;
			}

			/**
			 * @brief 计算到另一个控制点的距离
			 * @param other 另一个控制点
			 * @return 距离（米）
			 */
			double distanceTo(const ControlPoint& other) const {
				try {
					return NSUtils::GeometryManager::calculate3DDistance(position, other.position);
				}
				catch (const std::exception& e) {
					LOG_WARN("控制点距离计算失败: {}", e.what());
					return 0.0;
				}
			}

			/**
			 * @brief 估算在此控制点的总停留时间
			 * @return 预计停留时间（秒）
			 */
			double getEstimatedDwellTime() const {
				double total_time = required_loiter_time;

				// 添加载荷动作时间
				if (action) {
					total_time += action->getEstimatedDuration();
				}

				for (const auto& cmd : action_commands) {
					total_time += cmd.delay_before_seconds + cmd.timeout_seconds;
				}

				return total_time;
			}

			/**
			 * @brief 检查是否满足到达条件
			 * @param current_pos 当前位置
			 * @param current_heading 当前航向（度）
			 * @return true表示满足到达条件
			 */
			bool isArrivalConditionMet(const WGS84Point& current_pos,
									  double current_heading = 0.0) const {
				try {
					// 检查位置容差
					double distance = NSUtils::GeometryManager::calculate3DDistance(position, current_pos);
					double pos_tolerance = getPositionToleranceOrDefault();
					if (distance > pos_tolerance) {
						return false;
					}

					// 检查高度容差
					double alt_diff = std::abs(current_pos.altitude - required_altitude);
					double alt_tolerance = getAltitudeToleranceOrDefault();
					if (alt_diff > alt_tolerance) {
						return false;
					}

					// 检查航向容差
					if (hasHeadingRequirement()) {
						double heading_diff = std::abs(current_heading - required_heading_deg.value());
						// 处理角度环绕
						if (heading_diff > 180.0) {
							heading_diff = 360.0 - heading_diff;
						}
						double heading_tolerance = getHeadingToleranceOrDefault();
						if (heading_diff > heading_tolerance) {
							return false;
						}
					}

					return true;
				}
				catch (const std::exception& e) {
					LOG_WARN("控制点到达条件检查失败: {}", e.what());
					return false;
				}
			}
		};

		// --- 类型别名 ---
		using ControlPointList = std::vector<ControlPoint>;
		using ControlPointPtr = std::shared_ptr<ControlPoint>;
		using ControlPointConstPtr = std::shared_ptr<const ControlPoint>;

		// --- 工具函数 ---

		/**
		 * @brief 计算控制点列表的总路径长度
		 * @param points 控制点列表
		 * @return 总路径长度（米）
		 */
		inline double calculatePathLength(const ControlPointList& points) {
			if (points.size() < 2) return 0.0;

			double total_length = 0.0;
			try {
				for (size_t i = 1; i < points.size(); ++i) {
					total_length += NSUtils::GeometryManager::calculate3DDistance(
						points[i-1].position, points[i].position);
				}
			}
			catch (const std::exception& e) {
				LOG_WARN("控制点路径长度计算失败: {}", e.what());
			}

			return total_length;
		}

		/**
		 * @brief 计算控制点列表的估计飞行时间
		 * @param points 控制点列表
		 * @param average_speed 平均飞行速度（m/s）
		 * @return 估计飞行时间（秒）
		 */
		inline double calculateEstimatedFlightTime(const ControlPointList& points,
												  double average_speed = 10.0) {
			if (average_speed <= 0.0) return 0.0;

			double path_length = calculatePathLength(points);
			double flight_time = path_length / average_speed;

			// 添加每个控制点的停留时间
			for (const auto& point : points) {
				flight_time += point.getEstimatedDwellTime();
			}

			return flight_time;
		}

		/**
		 * @brief 验证控制点列表的有效性
		 * @param points 控制点列表
		 * @return true表示所有控制点都有效
		 */
		inline bool validateControlPointList(const ControlPointList& points) {
			if (points.empty()) {
				LOG_WARN("控制点列表为空");
				return false;
			}

			for (size_t i = 0; i < points.size(); ++i) {
				if (!points[i].isValid()) {
					LOG_WARN("控制点 #{} 无效: {}", i, points[i].getDetailedDescription());
					return false;
				}
			}

			return true;
		}

		/**
		 * @brief 查找具有指定名称的控制点
		 * @param points 控制点列表
		 * @param name 要查找的名称
		 * @return 找到的控制点指针，未找到返回nullptr
		 */
		inline const ControlPoint* findControlPointByName(const ControlPointList& points,
														 const std::string& name) {
			auto it = std::find_if(points.begin(), points.end(),
				[&name](const ControlPoint& cp) { return cp.name == name; });
			return (it != points.end()) ? &(*it) : nullptr;
		}

		/**
		 * @brief 过滤具有载荷动作的控制点
		 * @param points 控制点列表
		 * @return 包含载荷动作的控制点列表
		 */
		inline ControlPointList filterPointsWithPayloadActions(const ControlPointList& points) {
			ControlPointList result;
			std::copy_if(points.begin(), points.end(), std::back_inserter(result),
				[](const ControlPoint& cp) { return cp.hasAnyPayloadAction(); });
			return result;
		}

		/**
		 * @brief 按类型过滤控制点
		 * @param points 控制点列表
		 * @param type 要过滤的类型
		 * @return 指定类型的控制点列表
		 */
		inline ControlPointList filterPointsByType(const ControlPointList& points,
												   ControlPointType type) {
			ControlPointList result;
			std::copy_if(points.begin(), points.end(), std::back_inserter(result),
				[type](const ControlPoint& cp) { return cp.type == type; });
			return result;
		}

		/**
		 * @brief 创建简单的航路点
		 * @param position WGS84位置
		 * @param altitude 高度（米）
		 * @param name 点名称
		 * @return 创建的控制点
		 */
		inline ControlPoint createWaypoint(const WGS84Point& position,
										   double altitude = 100.0,
										   const std::string& name = "") {
			return ControlPoint(position, ControlPointType::WAYPOINT_MUST_PASS,
							   altitude, AltitudeType::ABOVE_GROUND_LEVEL, name);
		}

		/**
		 * @brief 创建带载荷动作的控制点
		 * @param position WGS84位置
		 * @param action_cmd 载荷动作命令
		 * @param loiter_time 停留时间（秒）
		 * @param name 点名称
		 * @return 创建的控制点
		 */
		inline ControlPoint createActionPoint(const WGS84Point& position,
											 const PayloadActionCommand& action_cmd,
											 double loiter_time = 5.0,
											 const std::string& name = "") {
			ControlPoint cp(position, ControlPointType::WAYPOINT_MUST_PASS,
						   position.altitude, AltitudeType::ABSOLUTE_ALTITUDE, name);
			cp.setLoiterTime(loiter_time).addActionCommand(action_cmd);
			return cp;
		}

		// --- 载荷动作工厂方法 ---

		/**
		 * @brief 创建拍照动作
		 * @param camera_id 相机ID
		 * @param photo_count 拍照数量
		 * @param interval 拍照间隔（秒）
		 * @return 拍照动作指针
		 */
		inline std::shared_ptr<PhotoAction> createPhotoAction(const std::string& camera_id = "",
															 int photo_count = 1,
															 double interval = 0.0) {
			return std::make_shared<PhotoAction>(camera_id, photo_count, interval);
		}

		/**
		 * @brief 创建投放动作
		 * @param dropper_id 投放器ID
		 * @param drop_count 投放数量
		 * @param interval 投放间隔（秒）
		 * @param drop_type 投放物类型
		 * @return 投放动作指针
		 */
		inline std::shared_ptr<DropAction> createDropAction(const std::string& dropper_id = "",
														   int drop_count = 1,
														   double interval = 0.0,
														   const std::string& drop_type = "") {
			return std::make_shared<DropAction>(dropper_id, drop_count, interval, drop_type);
		}

		/**
		 * @brief 创建扫描动作
		 * @param scanner_id 扫描器ID
		 * @param duration 扫描时长（秒）
		 * @param mode 扫描模式
		 * @param resolution 扫描分辨率
		 * @return 扫描动作指针
		 */
		inline std::shared_ptr<ScanAction> createScanAction(const std::string& scanner_id = "",
														   double duration = 5.0,
														   const std::string& mode = "normal",
														   double resolution = 1.0) {
			return std::make_shared<ScanAction>(scanner_id, duration, mode, resolution);
		}

		/**
		 * @brief 创建拍照控制点
		 * @param position WGS84位置
		 * @param camera_id 相机ID
		 * @param photo_count 拍照数量
		 * @param name 点名称
		 * @return 带拍照动作的控制点
		 */
		inline ControlPoint createPhotoPoint(const WGS84Point& position,
											const std::string& camera_id = "",
											int photo_count = 1,
											const std::string& name = "") {
			ControlPoint cp(position, ControlPointType::WAYPOINT_MUST_PASS,
						   position.altitude, AltitudeType::ABSOLUTE_ALTITUDE, name);
			auto photo_action = createPhotoAction(camera_id, photo_count);
			cp.setPayloadAction(photo_action).setLoiterTime(photo_action->getEstimatedDuration());
			return cp;
		}

		/**
		 * @brief 创建投放控制点
		 * @param position WGS84位置
		 * @param dropper_id 投放器ID
		 * @param drop_count 投放数量
		 * @param drop_type 投放物类型
		 * @param name 点名称
		 * @return 带投放动作的控制点
		 */
		inline ControlPoint createDropPoint(const WGS84Point& position,
										   const std::string& dropper_id = "",
										   int drop_count = 1,
										   const std::string& drop_type = "",
										   const std::string& name = "") {
			ControlPoint cp(position, ControlPointType::WAYPOINT_MUST_PASS,
						   position.altitude, AltitudeType::ABSOLUTE_ALTITUDE, name);
			auto drop_action = createDropAction(dropper_id, drop_count, 0.0, drop_type);
			cp.setPayloadAction(drop_action).setLoiterTime(drop_action->getEstimatedDuration());
			return cp;
		}

		/**
		 * @brief 创建扫描控制点
		 * @param position WGS84位置
		 * @param scanner_id 扫描器ID
		 * @param scan_duration 扫描时长（秒）
		 * @param scan_mode 扫描模式
		 * @param name 点名称
		 * @return 带扫描动作的控制点
		 */
		inline ControlPoint createScanPoint(const WGS84Point& position,
										   const std::string& scanner_id = "",
										   double scan_duration = 5.0,
										   const std::string& scan_mode = "normal",
										   const std::string& name = "") {
			ControlPoint cp(position, ControlPointType::WAYPOINT_MUST_PASS,
						   position.altitude, AltitudeType::ABSOLUTE_ALTITUDE, name);
			auto scan_action = createScanAction(scanner_id, scan_duration, scan_mode);
			cp.setPayloadAction(scan_action).setLoiterTime(scan_action->getEstimatedDuration());
			return cp;
		}

	} // namespace NSMission
} // namespace NSDrones