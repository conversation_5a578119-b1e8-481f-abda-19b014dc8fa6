// include/mission/execution_strategy.h
#pragma once

#include "core/types.h"
#include "params/parameters.h"
#include <string>
#include <memory>             
#include <map>
#include <optional>
#include <algorithm> 

namespace NSDrones {
	namespace NSMission {
		using namespace NSDrones::NSUtils;
		using namespace NSDrones::NSCore;
		using namespace NSDrones::NSParams;

		// --- 策略基类 ---
		/**
		 * @class ExecutionStrategyBase
		 * @brief 所有执行策略的基类。
		 */
		class ITaskStrategy {
		public:
			virtual ~ITaskStrategy() = default;
			/** @brief 获取策略的类型标识符 */
			virtual std::string getName() const = 0;
		};
		using ITaskStrategyPtr = std::shared_ptr<ITaskStrategy>;
		using ConstITaskStrategyPtr = std::shared_ptr<const ITaskStrategy>;

		// --- 具体策略定义 ---

		/** @brief 高度策略 */
		class AltitudeStrategy : public ITaskStrategy {
		public:
			AltitudeType height_type = AltitudeType::ABOVE_GROUND_LEVEL; // 高度类型
			double value = 100.0;                                   // 高度值 (米)

			AltitudeStrategy(AltitudeType ht, double val) : height_type(ht), value(val) {}
			std::string getName() const override { return "Altitude"; }
		};

		/** @brief 速度策略 */
		class SpeedStrategy : public ITaskStrategy {
		public:
			double desired_speed = 10.0; // 期望速度 (m/s)
			// 可以添加更复杂的策略，如速度区间、加减速曲线等

			explicit SpeedStrategy(double speed) : desired_speed(std::max(0.0, speed)) {}
			std::string getName() const override { return "Speed"; }
		};

		/** @brief 路径约束策略 */
		class PathConstraintStrategy : public ITaskStrategy {
		public:
			double max_curvature = Constants::INF;       // 最大曲率 (1/米)
			double max_slope_deg = 90.0;                 // 最大坡度 (度)
			double min_altitude_agl = -Constants::INF;   // 最小离地高度 (米)
			double max_altitude_agl = Constants::INF;    // 最大离地高度 (米)
			double max_altitude_msl = Constants::INF;    // 最大海拔高度 (米)
			double required_width = 0.0;                 // 航线要求的宽度 (米)，用于碰撞检测

			std::string getName() const override { return "PathConstraint"; }
		};

		/** @brief 载荷控制策略  */
		class PayloadControlStrategy : public ITaskStrategy {
		public:
			std::string payload_id; // 目标载荷 ID
			std::string command;    // 控制命令 (例如 "TakePhoto", "SetZoom")
			std::map<std::string, ParamValue> parameters; // 命令参数

			std::string getName() const override { return "PayloadControl"; }
		};

		// --- 策略集合 ---
		// 使用 map 存储不同类型的策略，键为策略类型字符串
		using ITaskStrategyMap = std::map<std::string, ITaskStrategyPtr>;
	} // namespace NSMission
} // namespace NSDrones