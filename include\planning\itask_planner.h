// include/planning/itask_planner.h
#pragma once

#include "core/types.h"
#include "uav/uav_fwd.h"
#include "environment/environment_fwd.h"
#include "mission/task_strategies.h"
#include "planning/planning_result.h"
#include "algorithm/path_planner/ipath_planner.h"
#include "algorithm/trajectory_optimizer/itrajectory_optimizer.h"
#include "algorithm/evaluator/itrajectory_evaluator.h"
#include <vector>
#include <memory>
#include <map>
#include <utility>
#include "nlohmann/json.hpp"

// 前向声明
namespace NSDrones {
	namespace NSMission { class ControlPoint; }
}

namespace NSDrones {
	namespace NSPlanning {

		/**
		 * @class ITaskPlanner
		 * @brief 任务规划器抽象基类接口
		 *
		 * 定义了特定任务类型规划器的核心接口，负责将高级任务定义转换为具体的无人机航线。
		 *
		 * ## 核心职责
		 * - **任务解析**: 解析任务定义和约束条件
		 * - **路径规划**: 利用底层路径规划器生成几何路径
		 * - **轨迹优化**: 使用轨迹优化器进行平滑和时间参数化
		 * - **安全检查**: 验证路径的安全性和可行性
		 * - **结果封装**: 生成标准化的规划结果
		 *
		 * ## 架构设计
		 * - **组合模式**: 组合路径规划器、轨迹优化器等组件
		 * - **模板方法**: 提供通用的规划流程框架
		 * - **策略模式**: 支持不同的任务策略配置
		 */
		class ITaskPlanner {
		public:
			// === 生命周期管理 ===

			/**
			 * @brief 构造函数
			 * @throws DroneException 如果环境未初始化
			 * @note 所有算法组件（路径规划器、轨迹优化器等）都从Environment中获取
			 */
			ITaskPlanner();

			/** @brief 虚析构函数 */
			virtual ~ITaskPlanner() = default;

			// === 禁止拷贝和移动 ===
			ITaskPlanner(const ITaskPlanner&) = delete;
			ITaskPlanner& operator=(const ITaskPlanner&) = delete;
			ITaskPlanner(ITaskPlanner&&) = delete;
			ITaskPlanner& operator=(ITaskPlanner&&) = delete;

			// === 核心接口方法 ===

			/**
			 * @brief 规划指定的任务（纯虚函数）
			 * @param task 要规划的任务对象
			 * @param assigned_uavs 分配给此任务的无人机列表
			 * @param start_states 每个无人机的起始状态映射（ID -> UavState）
			 * @return 包含规划结果（航线、告警等）的PlanningResult对象
			 *
			 * ## 规划流程
			 * 1. 解析任务定义和约束条件
			 * 2. 生成几何路径
			 * 3. 进行轨迹优化和时间参数化
			 * 4. 执行安全性检查
			 * 5. 封装规划结果
			 */
			virtual PlanningResult planTask(const NSMission::Task& task,
				const std::vector<NSUav::UavPtr>& assigned_uavs,
				const std::map<NSUtils::ObjectID, NSUav::UavState>& start_states) = 0;

			/**
			 * @brief 检查规划器是否支持指定任务类型（纯虚函数）
			 * @param task_type 任务类型枚举值
			 * @return 支持返回true，否则返回false
			 */
			virtual bool isTaskTypeSupported(NSMission::TaskType task_type) const = 0;

			/**
			 * @brief 初始化任务规划器（纯虚函数）
			 * @param params 配置参数对象
			 * @param raw_config 原始JSON配置
			 * @return 初始化成功返回true，否则返回false
			 */
			virtual bool initialize(std::shared_ptr<NSParams::ParamValues> params,
									const nlohmann::json& raw_config) = 0;

		protected:
			// === 环境和算法组件访问接口 ===

			/**
			 * @brief 获取环境实例
			 * @return 环境的共享指针，如果环境未初始化则返回nullptr
			 * @note 使用单例模式获取环境，子类可直接调用
			 */
			std::shared_ptr<NSEnvironment::Environment> getEnvironment() const;

			/**
			 * @brief 从环境获取路径规划器
			 * @return 路径规划器的共享指针，如果未配置则返回nullptr
			 */
			IPathPlannerPtr getPathPlanner() const;

			/**
			 * @brief 从环境获取轨迹优化器
			 * @return 轨迹优化器的共享指针，如果未配置则返回nullptr
			 */
			ITrajectoryOptimizerPtr getTrajectoryOptimizer() const;

			/**
			 * @brief 从环境获取轨迹评估器
			 * @return 轨迹评估器的共享指针，如果未配置则返回nullptr
			 */
			ITrajectoryEvaluatorPtr getTrajectoryEvaluator() const;

			// === 通用任务规划辅助方法 ===

			/**
			 * @brief 验证任务基本信息和分配的无人机
			 * @param task 要验证的任务
			 * @param expected_type 期望的任务类型
			 * @param assigned_uavs 分配的无人机列表
			 * @param result 用于记录错误信息的结果对象
			 * @return 验证通过返回true，否则返回false
			 */
			bool validateTaskBasics(const NSMission::Task& task,
								   NSMission::TaskType expected_type,
								   const std::vector<NSUav::UavPtr>& assigned_uavs,
								   PlanningResult& result) const;

			/**
			 * @brief 验证无人机状态和动力学模型
			 * @param uav 要验证的无人机
			 * @param start_states 起始状态映射
			 * @param task_id 任务ID（用于错误报告）
			 * @param result 用于记录错误信息的结果对象
			 * @return 验证通过返回true，否则返回false
			 */
			bool validateUavState(const NSUav::UavPtr& uav,
								 const std::map<NSUtils::ObjectID, NSUav::UavState>& start_states,
								 const NSUtils::ObjectID& task_id,
								 PlanningResult& result) const;

			/**
			 * @brief 规划从起始点到目标点的路径
			 * @param start_state 起始状态
			 * @param target_wgs84 目标WGS84位置
			 * @param uav 执行路径的无人机
			 * @param task 关联的任务（用于获取路径约束）
			 * @param result 用于记录错误信息的结果对象
			 * @return 成功返回路径点列表，失败返回空列表
			 */
			std::vector<NSCore::EcefPoint> planPathToTarget(
				const NSUav::UavState& start_state,
				const NSCore::WGS84Point& target_wgs84,
				const NSUav::UavPtr& uav,
				const NSMission::Task& task,
				PlanningResult& result) const;

			// === 通用辅助方法（子类可使用） ===

			/**
			 * @brief 获取控制点的绝对ECEF坐标位置
			 * @param cp 控制点
			 * @param task 包含此控制点的任务对象，用于获取高度策略
			 * @return pair，包含绝对ECEF位置和操作是否成功的布尔值
			 * @note 考虑任务策略中的高度设置和坐标转换
			 */
			std::pair<NSCore::EcefPoint, bool> getAbsolutePosition(
				const NSMission::ControlPoint& cp,
				const NSMission::Task& task) const;

			/**
			 * @brief 生成简单的直线航段
			 * @param start_point 起始航路点（包含时间、速度等）
			 * @param end_wgs84_pos 目标WGS84位置
			 * @param speed 期望的恒定速度
			 * @param dynamics 无人机动力学模型
			 * @param uav_id 无人机ID
			 * @return 生成的航段（包含起点和终点），如果速度无效则为空
			 */
			PlannedRoute generateLinearSegment(const RoutePoint& start_point,
				const NSCore::WGS84Point& end_wgs84_pos,
				double speed,
				const NSUav::IDynamicModel& dynamics,
				const NSUtils::ObjectID& uav_id) const;

			/**
			 * @brief 检查航段中可能产生的告警
			 * @param segment 要检查的航段
			 * @param uav_id 执行此航段的无人机ID（用于告警关联）
			 * @param result 用于添加告警的PlanningResult对象引用
			 * @param task_id 关联的任务ID（可选）
			 * @note 检查进入/离开告警区、禁飞区等情况
			 */
			void checkSegmentWarnings(const PlannedRoute& segment,
									  const NSUtils::ObjectID& uav_id,
									  PlanningResult& result,
									  const NSUtils::ObjectID& task_id = NSUtils::INVALID_OBJECT_ID) const;

			/**
			 * @brief 几何路径平滑和时间参数化
			 *
			 * 执行完整的轨迹处理流程：
			 * 1. 匀速时间参数化
			 * 2. 轨迹优化（如果配置了优化器）
			 * 3. 应用任务策略
			 *
			 * @param geometric_path 输入的几何路径点（WGS84坐标）
			 * @param uav 执行此路径的无人机指针
			 * @param start_state 无人机在路径起点的状态
			 * @param desired_speed 期望速度（m/s）
			 * @param optimized_segment 输出参数：优化后的轨迹段
			 * @param result_ptr 指向PlanningResult的指针（可选，用于添加告警）
			 * @param strategies 应用于此轨迹段的策略（可选）
			 * @return 成功生成有效轨迹返回true
			 */
			bool smoothAndTimeParameterize(const std::vector<NSCore::WGS84Point>& geometric_path,
				const NSUav::UavPtr& uav,
				const NSUav::UavState& start_state,
				double desired_speed,
				RouteSegment& optimized_segment,
				PlanningResult* result_ptr = nullptr,
				const NSMission::ITaskStrategyMap& strategies = {});

			/**
			 * @brief 几何路径平滑和时间参数化（ECEF坐标版本）
			 *
			 * 与WGS84版本功能相同，但输入为ECEF坐标系的路径点。
			 * 适用于已经转换为ECEF坐标的路径处理。
			 *
			 * @param geometric_path 输入几何路径（ECEF坐标）
			 * @param uav 无人机指针
			 * @param start_state 起始状态
			 * @param desired_speed 期望速度
			 * @param optimized_segment 输出参数：优化后的轨迹段
			 * @param result_ptr 指向PlanningResult的指针（可选）
			 * @param strategies 应用于此轨迹段的策略（可选）
			 * @return 成功生成有效轨迹返回true
			 */
			bool smoothAndTimeParameterizeECEF(const std::vector<NSCore::EcefPoint>& geometric_path,
				const NSUav::UavPtr& uav,
				const NSUav::UavState& start_state,
				double desired_speed,
				RouteSegment& optimized_segment,
				PlanningResult* result_ptr = nullptr,
				const NSMission::ITaskStrategyMap& strategies = {});

		private:
			// === 安全检查和约束验证（内部方法） ===

			/**
			 * @brief 检查航段是否满足区域约束
			 * @param segment 要检查的航段
			 * @param zone 要检查的区域
			 * @return 满足约束返回true
			 * @note 子类可重写此方法以实现特定区域类型的约束逻辑
			 */
			virtual bool isZoneConstraintSatisfied(const RouteSegment& segment,
													const NSEnvironment::ConstZonePtr& zone) const;

			/**
			 * @brief 检查路径点是否满足安全约束
			 * @param point 要检查的路径点
			 * @param uav_id 无人机ID
			 * @param task_id 任务ID
			 * @param result 规划结果，用于添加告警
			 * @param zone_types_to_check 需要检查的特定区域类型列表
			 * @return 满足安全约束返回true
			 */
			bool checkSafetyConstraints(const RoutePoint& point,
										const NSUtils::ObjectID& uav_id,
										const NSUtils::ObjectID& task_id,
										PlanningResult& result,
										const std::vector<NSCore::ZoneType>& zone_types_to_check) const;

			/**
			 * @brief 检查路径段是否安全
			 * @param wgs84_p1 路径段起点（WGS84坐标）
			 * @param wgs84_p2 路径段终点（WGS84坐标）
			 * @param t1 起点时间
			 * @param t2 终点时间
			 * @param uav_id 无人机ID
			 * @param task_id 任务ID
			 * @param result 规划结果，用于添加告警
			 * @param zone_types_to_check 需要检查的特定区域类型列表
			 * @return 路径段安全返回true
			 */
			bool isPathSegmentSafe(const NSCore::WGS84Point& wgs84_p1,
								   const NSCore::WGS84Point& wgs84_p2,
								   NSCore::Time t1,
								   NSCore::Time t2,
								   const NSUtils::ObjectID& uav_id,
								   const NSUtils::ObjectID& task_id,
								   PlanningResult& result,
								   const std::vector<NSCore::ZoneType>& zone_types_to_check) const;

		};

		// === 类型别名定义 ===
		using TaskPlannerPtr = std::shared_ptr<ITaskPlanner>;           ///< 任务规划器智能指针类型

	} // namespace NSPlanning
} // namespace NSDrones