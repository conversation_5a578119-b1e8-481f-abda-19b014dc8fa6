// src/planning/mission_planner.cpp
#include "core/types.h"
#include "planning/mission_planner.h"
#include "environment/environment.h"
#include "mission/mission.h"
#include "mission/task.h"
#include "uav/uav.h"
#include "algorithm/allocator/itask_allocator.h"
#include "algorithm/allocator/task_allocator.h"
#include "planning/itask_planner.h"
#include "algorithm/evaluator/itrajectory_evaluator.h"
#include "utils/geometry_manager.h"
#include "utils/logging.h"
#include "utils/enum_utils.h"
#include "params/parameters.h"
#include "params/paramregistry.h"
#include <spdlog/fmt/format.h>
#include <thread>
#include <future>
#include <vector>
#include <map>
#include <set>
#include <algorithm>
#include <numeric>
#include <cmath>
#include <chrono>
#include <stdexcept>
#include <typeinfo>

namespace NSDrones {
	namespace NSPlanning {

		// === 构造函数实现 ===

		MissionPlanner::MissionPlanner() {
			LOG_INFO("任务规划器: MissionPlanner初始化完成");
		}

		
		void MissionPlanner::loadParams() {
			// 暂时没有 MissionPlanner 特有的参数需要加载
			LOG_DEBUG("MissionPlanner::loadParams - 当前无特定参数加载。");
		}

		// === 环境和算法组件访问实现 ===

		std::shared_ptr<NSEnvironment::Environment> MissionPlanner::getEnvironment() const {
			return NSEnvironment::Environment::getInstance();
		}

		IPathPlannerPtr MissionPlanner::getPathPlanner() const {
			auto environment = getEnvironment();
			if (!environment) {
				LOG_ERROR("任务规划器: 无法获取环境实例");
				return nullptr;
			}
			return environment->getPathPlanner();
		}

		ITrajectoryOptimizerPtr MissionPlanner::getTrajectoryOptimizer() const {
			auto environment = getEnvironment();
			if (!environment) {
				LOG_ERROR("任务规划器: 无法获取环境实例");
				return nullptr;
			}
			return environment->getTrajectoryOptimizer();
		}

		ITrajectoryEvaluatorPtr MissionPlanner::getTrajectoryEvaluator() const {
			auto environment = getEnvironment();
			if (!environment) {
				LOG_ERROR("任务规划器: 无法获取环境实例");
				return nullptr;
			}
			return environment->getTrajectoryEvaluator();
		}

		ITaskAllocatorPtr MissionPlanner::getTaskAllocator() const {
			auto environment = getEnvironment();
			if (!environment) {
				LOG_ERROR("任务规划器: 无法获取环境实例");
				return nullptr;
			}
			return environment->getTaskAllocator();
		}

		// === 规划器注册管理实现 ===

		void MissionPlanner::registerTaskPlanner(NSCore::TaskType task_type, TaskPlannerPtr planner) {
			if (planner) {
				std::string type_str = NSUtils::enumToString(task_type);
				if (task_planners_.count(task_type)) {
					LOG_WARN("任务规划器: 覆盖任务类型[{}]的规划器", type_str);
				}
				task_planners_[task_type] = planner;
				LOG_INFO("任务规划器: 已注册任务类型[{}]的规划器", type_str);
			} else {
				LOG_WARN("任务规划器: 尝试注册空的规划器，任务类型[{}]，已忽略",
					NSUtils::enumToString(task_type));
			}
		}

		// === 无人机资源管理实现 ===

		std::vector<NSUav::UavPtr> MissionPlanner::getAvailableUAVs() const {
			std::lock_guard<std::mutex> lock(uav_list_mutex_);
			return available_uavs_; // 返回列表副本
		}

		void MissionPlanner::addAvailableUAV(NSUav::UavPtr uav) {
			if (!uav) {
				LOG_WARN("任务规划器: 尝试添加空的UAV指针，已忽略");
				return;
			}

			const NSUtils::ObjectID& uav_id = uav->getId();
			std::lock_guard<std::mutex> lock(uav_list_mutex_);

			// 检查是否已存在
			bool found = std::any_of(available_uavs_.begin(), available_uavs_.end(),
				[&uav_id](const NSUav::UavPtr& existing_uav) {
					return existing_uav && existing_uav->getId() == uav_id;
				});

			if (!found) {
				available_uavs_.push_back(uav);
				LOG_INFO("任务规划器: 已添加可用无人机{}，当前总数{}", uav_id, available_uavs_.size());
			} else {
				LOG_DEBUG("任务规划器: 无人机{}已存在，忽略添加请求", uav_id);
			}
		}

		bool MissionPlanner::removeAvailableUAV(const NSUtils::ObjectID& uav_id) {
			if (!NSUtils::isValidObjectID(uav_id)) {
				LOG_WARN("任务规划器: 尝试移除无效的UAV ID '{}'，已忽略", uav_id);
				return false;
			}

			std::lock_guard<std::mutex> lock(uav_list_mutex_);

			// 使用remove_if + erase删除匹配的无人机
			auto new_end = std::remove_if(available_uavs_.begin(), available_uavs_.end(),
				[&uav_id](const NSUav::UavPtr& uav) {
					return uav && uav->getId() == uav_id;
				});

			if (new_end != available_uavs_.end()) {
				available_uavs_.erase(new_end, available_uavs_.end());
				LOG_INFO("任务规划器: 已移除可用无人机{}，当前总数{}", uav_id, available_uavs_.size());
				return true;
			} else {
				LOG_WARN("任务规划器: 未找到要移除的无人机ID: {}", uav_id);
				return false;
			}
		}

		// === 规划器查找实现 ===

		TaskPlannerPtr MissionPlanner::findPlannerForTask(const NSMission::Task& task) const {
			auto it = task_planners_.find(task.getType());
			if (it != task_planners_.end()) {
				return it->second;
			} else {
				LOG_WARN("任务规划器: 未找到任务类型[{}]的注册规划器",
					NSUtils::enumToString(task.getType()));
				return nullptr;
			}
		}

		// === 核心规划接口实现（重构后的三阶段流程） ===

		PlanningResult MissionPlanner::planMission(const NSMission::Mission& mission) {
			LOG_INFO("任务规划器: 开始规划任务计划，ID: {}", mission.getId());

			PlanningResult result;
			result.success = true;
			result.message = "规划初始化";

			// 获取可用无人机
			std::vector<NSUav::UavPtr> available_uavs = getAvailableUAVs();
			LOG_INFO("任务规划器: 当前可用无人机数量: {}", available_uavs.size());

			if (available_uavs.empty() && !mission.getTasks().empty()) {
				result.success = false;
				result.message = "无可用无人机执行任务";
				LOG_ERROR("任务规划器: {}", result.message);
				return result;
			}

			// === 第1步：任务分解和分配阶段 ===
			LOG_INFO("=== 阶段1：任务分解和分配 ===");
			auto allocator = getTaskAllocator();
			if (!allocator) {
				result.success = false;
				result.message = "任务分配器未配置";
				LOG_ERROR("任务规划器: {}", result.message);
				return result;
			}

			// 注意：ITaskAllocator 必须正确设置 SubTaskTarget 的以下字段：
			// - task_type: 从父任务继承的明确任务类型
			// - parent_task_ref: 父任务对象的引用
			// 这样可以避免在后续步骤中进行类型推断
			auto mission_decomposition = allocator->decomposeAndAllocateMission(
				mission, available_uavs, getCurrentUavStates());

			if (!mission_decomposition.success) {
				result.success = false;
				result.message = "任务分解失败: " + mission_decomposition.message;
				LOG_ERROR("任务规划器: {}", result.message);
				return result;
			}

			result.decomposition = mission_decomposition;
			LOG_INFO("任务分解完成，共 {} 个任务被分解", mission_decomposition.task_decompositions.size());

			// === 第2步：单机单任务规划阶段 ===
			LOG_INFO("=== 阶段2：单机单任务规划 ===");
			for (const auto& task_decomp : mission_decomposition.task_decompositions) {
				for (const auto& sub_assignment : task_decomp.sub_assignments) {

					// 找到合适的TaskPlanner
					auto planner = findPlannerForSubTask(sub_assignment.sub_target);
					if (!planner) {
						LOG_WARN("未找到子任务 {} 的规划器，跳过", sub_assignment.sub_task_id);
						continue;
					}

					// 执行单机规划
					auto single_result = planSingleTaskWithAssignment(mission, sub_assignment);

					// 收集结果
					result.task_results.push_back(single_result);

					if (!single_result.success) {
						LOG_WARN("子任务 {} 规划失败: {}",
							single_result.sub_task_id, single_result.message);
						result.success = false;
						result.message += "子任务 " + single_result.sub_task_id + " 规划失败; ";
					} else {
						LOG_DEBUG("子任务 {} 规划成功，生成 {} 个轨迹点",
							single_result.sub_task_id, single_result.trajectory.size());
					}
				}
			}

			// === 第3步：多机协调优化阶段 ===
			LOG_INFO("=== 阶段3：多机协调优化 ===");
			if (result.success) {
				optimizeMultiUavCoordination(result);
				evaluateGlobalPerformance(result);
			}

			LOG_INFO("任务规划完成，任务计划 ID: {}. 最终成功: {}. 生成 {} 个子任务结果, {} 条全局告警。",
				mission.getId(), result.success, result.task_results.size(), result.global_warnings.size());

			return result;
		}

		// === 新的辅助方法实现 ===

		SingleTaskPlanningResult MissionPlanner::planSingleTaskWithAssignment(
			const NSMission::Mission& mission,
			const SubTaskAssignment& assignment) {

			LOG_DEBUG("开始规划子任务：{}, UAV: {}, 任务类型: {}",
				assignment.sub_task_id, assignment.assigned_uav_id,
				NSUtils::enumToString(assignment.getTaskType()));

			SingleTaskPlanningResult result;
			result.success = false;
			result.sub_task_id = assignment.sub_task_id;
			result.uav_id = assignment.assigned_uav_id;

			try {
				// 第1步：验证输入数据
				if (assignment.getTaskType() == NSCore::TaskType::UNKNOWN) {
					result.message = "子任务类型未知";
					LOG_ERROR("子任务 {} 的任务类型为UNKNOWN", assignment.sub_task_id);
					return result;
				}

				// 第2步：构建规划请求
				auto request = buildSingleTaskRequest(assignment, mission);
				if (!request.uav) {
					result.message = "未找到分配的无人机";
					LOG_ERROR("子任务 {} 分配的无人机 {} 不存在",
						assignment.sub_task_id, assignment.assigned_uav_id);
					return result;
				}

				if (!request.original_task) {
					result.message = "未找到原始任务";
					LOG_ERROR("子任务 {} 的原始任务 {} 不存在",
						assignment.sub_task_id, assignment.sub_target.parent_task_id);
					return result;
				}

				// 第3步：找到合适的规划器
				auto planner = findPlannerForSubTask(assignment.sub_target);
				if (!planner) {
					result.message = "未找到合适的规划器";
					LOG_WARN("子任务 {} (类型: {}) 未找到合适的规划器",
						assignment.sub_task_id, NSUtils::enumToString(assignment.getTaskType()));
					return result;
				}

				LOG_DEBUG("使用规划器执行子任务 {} 的规划", assignment.sub_task_id);

				// 第4步：执行规划
				result = planner->planSingleTask(request);

				// 第5步：验证规划结果
				if (result.success) {
					if (result.trajectory.isEmpty()) {
						result.success = false;
						result.message = "规划成功但轨迹为空";
						LOG_WARN("子任务 {} 规划成功但生成的轨迹为空", assignment.sub_task_id);
					} else {
						LOG_INFO("子任务 {} 规划成功，生成 {} 个轨迹点",
							assignment.sub_task_id, result.trajectory.getPoints().size());
					}
				} else {
					LOG_WARN("子任务 {} 规划失败: {}", assignment.sub_task_id, result.message);
				}

			} catch (const std::exception& e) {
				result.message = "规划异常: " + std::string(e.what());
				LOG_ERROR("子任务 {} 规划异常: {}", assignment.sub_task_id, e.what());
			}

			return result;
		}

		void MissionPlanner::optimizeMultiUavCoordination(PlanningResult& result) {
			LOG_INFO("开始多机协调优化");

			if (result.task_results.size() <= 1) {
				LOG_DEBUG("只有一个或零个任务结果，无需多机协调");
				return;
			}

			// 第1步：检测轨迹冲突
			auto conflicts = detectTrajectoryConflicts(result);
			if (conflicts.empty()) {
				LOG_DEBUG("未检测到轨迹冲突");
				return;
			}

			LOG_INFO("检测到 {} 个轨迹冲突，开始解决", conflicts.size());

			// 第2步：解决冲突
			for (const auto& conflict : conflicts) {
				if (!resolveTrajectoryConflict(conflict, result)) {
					LOG_WARN("无法解决UAV {} 和 {} 之间的冲突",
						conflict.uav1_id, conflict.uav2_id);

					// 添加警告
					WarningEvent warning;
					warning.wtype = WarningType::PROXIMITY_ALERT;
					warning.description = fmt::format("UAV {} 和 {} 轨迹存在冲突",
						conflict.uav1_id, conflict.uav2_id);
					warning.time_stamp = conflict.conflict_time;
					warning.location = conflict.conflict_location;
					result.global_warnings.push_back(warning);
				}
			}

			// 第3步：验证协调结果
			auto remaining_conflicts = detectTrajectoryConflicts(result);
			if (remaining_conflicts.empty()) {
				LOG_INFO("多机协调优化完成，所有冲突已解决");
			} else {
				LOG_WARN("多机协调优化完成，仍有 {} 个未解决的冲突", remaining_conflicts.size());
			}
		}

		std::vector<MissionPlanner::TrajectoryConflict> MissionPlanner::detectTrajectoryConflicts(
			const PlanningResult& result) const {

			std::vector<TrajectoryConflict> conflicts;

			// 获取所有UAV的完整轨迹
			auto all_trajectories = result.getAllCompleteTrajectories();

			// 两两比较轨迹
			for (auto it1 = all_trajectories.begin(); it1 != all_trajectories.end(); ++it1) {
				for (auto it2 = std::next(it1); it2 != all_trajectories.end(); ++it2) {

					const auto& uav1_id = it1->first;
					const auto& uav2_id = it2->first;
					const auto& traj1 = it1->second;
					const auto& traj2 = it2->second;

					// 检测两条轨迹的冲突
					auto trajectory_conflicts = detectConflictBetweenTrajectories(
						uav1_id, traj1, uav2_id, traj2);

					conflicts.insert(conflicts.end(),
						trajectory_conflicts.begin(), trajectory_conflicts.end());
				}
			}

			return conflicts;
		}

		bool MissionPlanner::resolveTrajectoryConflict(
			const TrajectoryConflict& conflict, PlanningResult& result) const {

			LOG_INFO("尝试解决UAV {} 和 {} 在时间 {:.2f}s 的冲突",
				conflict.uav1_id, conflict.uav2_id, conflict.conflict_time);

			// 策略1：时间偏移
			if (tryTimeShiftResolution(conflict, result)) {
				LOG_DEBUG("通过时间偏移解决了冲突");
				return true;
			}

			// 策略2：高度分离
			if (tryAltitudeSeparationResolution(conflict, result)) {
				LOG_DEBUG("通过高度分离解决了冲突");
				return true;
			}

			// 策略3：路径重规划
			if (tryPathReplanning(conflict, result)) {
				LOG_DEBUG("通过路径重规划解决了冲突");
				return true;
			}

			LOG_WARN("无法解决冲突，已尝试所有策略");
			return false;
		}

		std::vector<MissionPlanner::TrajectoryConflict> MissionPlanner::detectConflictBetweenTrajectories(
			const ObjectID& uav1_id, const Trajectory& traj1,
			const ObjectID& uav2_id, const Trajectory& traj2) const {

			std::vector<TrajectoryConflict> conflicts;
			const double SAFE_DISTANCE = 10.0; // 安全距离10米
			const double TIME_TOLERANCE = 0.1; // 时间容差0.1秒

			const auto& points1 = traj1.getPoints();
			const auto& points2 = traj2.getPoints();

			if (points1.empty() || points2.empty()) {
				return conflicts;
			}

			// 遍历轨迹1的每个点
			for (size_t i = 0; i < points1.size(); ++i) {
				const auto& p1 = points1[i];

				// 在轨迹2中找到时间最接近的点
				for (size_t j = 0; j < points2.size(); ++j) {
					const auto& p2 = points2[j];

					// 检查时间是否接近
					if (std::abs(p1.time_stamp - p2.time_stamp) <= TIME_TOLERANCE) {

						// 计算距离
						double distance = GeometryManager::calculate3DDistance(p1.position, p2.position);

						if (distance < SAFE_DISTANCE) {
							TrajectoryConflict conflict;
							conflict.uav1_id = uav1_id;
							conflict.uav2_id = uav2_id;
							conflict.conflict_time = p1.time_stamp;
							conflict.conflict_location = p1.position;
							conflict.min_distance = distance;
							conflict.safe_distance = SAFE_DISTANCE;

							conflicts.push_back(conflict);

							LOG_DEBUG("检测到冲突：UAV {} 和 {} 在时间 {:.2f}s，距离 {:.2f}m",
								uav1_id, uav2_id, conflict.conflict_time, distance);
						}
					}
				}
			}

			return conflicts;
		}

		bool MissionPlanner::tryTimeShiftResolution(
			const TrajectoryConflict& conflict, PlanningResult& result) const {

			// 策略：将其中一个UAV的轨迹延迟一定时间
			const double TIME_SHIFT = 5.0; // 延迟5秒

			// 选择延迟优先级较低的UAV（这里简单选择ID较大的）
			ObjectID uav_to_shift = (conflict.uav1_id > conflict.uav2_id) ?
				conflict.uav1_id : conflict.uav2_id;

			// 找到对应的任务结果并调整时间
			for (auto& task_result : result.task_results) {
				if (task_result.uav_id == uav_to_shift) {
					auto& points = task_result.trajectory.getPoints();
					for (auto& point : points) {
						point.time_stamp += TIME_SHIFT;
					}

					LOG_DEBUG("对UAV {} 的轨迹应用了 {:.1f}s 的时间偏移", uav_to_shift, TIME_SHIFT);
					return true;
				}
			}

			return false;
		}

		bool MissionPlanner::tryAltitudeSeparationResolution(
			const TrajectoryConflict& conflict, PlanningResult& result) const {

			// 策略：将其中一个UAV的高度提升
			const double ALTITUDE_SEPARATION = 20.0; // 高度分离20米

			// 选择提升高度的UAV（这里简单选择ID较小的）
			ObjectID uav_to_elevate = (conflict.uav1_id < conflict.uav2_id) ?
				conflict.uav1_id : conflict.uav2_id;

			// 找到对应的任务结果并调整高度
			for (auto& task_result : result.task_results) {
				if (task_result.uav_id == uav_to_elevate) {
					auto& points = task_result.trajectory.getPoints();
					for (auto& point : points) {
						point.position.altitude += ALTITUDE_SEPARATION;
					}

					LOG_DEBUG("对UAV {} 的轨迹应用了 {:.1f}m 的高度分离",
						uav_to_elevate, ALTITUDE_SEPARATION);
					return true;
				}
			}

			return false;
		}

		bool MissionPlanner::tryPathReplanning(
			const TrajectoryConflict& conflict, PlanningResult& result) const {

			// 策略：重新规划其中一个UAV的路径
			// 这是最复杂的策略，需要调用路径规划器重新规划

			LOG_DEBUG("路径重规划策略当前未实现，需要调用路径规划器");

			// TODO: 实现路径重规划
			// 1. 选择要重规划的UAV
			// 2. 获取原始任务信息
			// 3. 调用路径规划器重新规划
			// 4. 更新轨迹

			return false;
		}

		void MissionPlanner::evaluateGlobalPerformance(PlanningResult& result) {
			LOG_INFO("开始全局性能评估");

			auto evaluator = getTrajectoryEvaluator();
			if (!evaluator) {
				LOG_WARN("未配置轨迹评估器，跳过全局性能评估");
				return;
			}

			// 为每个UAV的完整轨迹进行评估
			auto complete_trajectories = result.getAllCompleteTrajectories();
			for (const auto& [uav_id, trajectory] : complete_trajectories) {
				auto uav = getUavById(uav_id);
				if (!uav) {
					LOG_WARN("未找到UAV {}，跳过评估", uav_id);
					continue;
				}

				TrajectoryEvaluationRequest eval_request;
				eval_request.trajectory = trajectory;
				eval_request.uav = uav;

				auto metrics = evaluator->evaluate(eval_request);

				// 将评估结果设置到轨迹中
				for (auto& task_result : result.task_results) {
					if (task_result.uav_id == uav_id) {
						task_result.trajectory.setEvaluationMetrics(metrics);
						break;
					}
				}

				LOG_DEBUG("UAV {} 全局性能评估完成，可行性: {}", uav_id, metrics.is_feasible);
			}

			LOG_DEBUG("全局性能评估完成");
		}

		TaskPlannerPtr MissionPlanner::findPlannerForSubTask(const SubTaskTarget& sub_target) const {
			// 直接使用明确的任务类型查找规划器
			NSCore::TaskType task_type = sub_target.task_type;

			if (task_type == NSCore::TaskType::UNKNOWN) {
				LOG_ERROR("子任务 {} 的任务类型为UNKNOWN，无法查找规划器", sub_target.sub_target_id);
				return nullptr;
			}

			// 方法1：直接根据任务类型查找
			auto it = task_planners_.find(task_type);
			if (it != task_planners_.end() && it->second) {
				LOG_DEBUG("根据任务类型 {} 找到规划器", NSUtils::enumToString(task_type));
				return it->second;
			}

			// 方法2：如果直接查找失败，遍历已注册的规划器进行支持性检查
			for (const auto& [registered_type, planner] : task_planners_) {
				if (planner && planner->isSubTaskSupported(sub_target)) {
					LOG_DEBUG("通过支持性检查找到规划器，任务类型: {}", NSUtils::enumToString(registered_type));
					return planner;
				}
			}

			LOG_WARN("未找到支持子任务 {} (任务类型: {}) 的规划器",
				sub_target.sub_target_id, NSUtils::enumToString(task_type));
			return nullptr;
		}

		SingleTaskPlanningRequest MissionPlanner::buildSingleTaskRequest(
			const SubTaskAssignment& assignment,
			const NSMission::Mission& mission) const {

			SingleTaskPlanningRequest request;
			request.assignment = assignment;
			request.uav = getUavById(assignment.assigned_uav_id);

			// 使用新的数据结构：直接从assignment获取原始任务
			request.original_task = assignment.getParentTask();

			// 如果SubTaskTarget中没有父任务引用，则从Mission中查找
			if (!request.original_task) {
				request.original_task = findTaskInMission(assignment.sub_target.parent_task_id, mission);
			}

			if (!request.uav) {
				LOG_WARN("未找到UAV {}，构建请求失败", assignment.assigned_uav_id);
			}

			if (!request.original_task) {
				LOG_WARN("未找到原始任务 {}，构建请求失败", assignment.sub_target.parent_task_id);
			}

			// 查找兄弟子任务（同一父任务的其他子任务）
			request.sibling_sub_tasks = findSiblingSubTasks(assignment, mission);

			LOG_DEBUG("构建单任务规划请求：子任务 {}, UAV {}, 任务类型 {}, 兄弟任务数 {}",
				assignment.sub_task_id, assignment.assigned_uav_id,
				NSUtils::enumToString(assignment.getTaskType()), request.sibling_sub_tasks.size());

			return request;
		}

		std::vector<ObjectID> MissionPlanner::findSiblingSubTasks(
			const SubTaskAssignment& assignment,
			const NSMission::Mission& mission) const {

			std::vector<ObjectID> siblings;

			// 简化实现：由于我们没有存储完整的分解结果，
			// 这里只能返回空列表
			// 在实际实现中，应该从当前的分解结果中查找同一父任务的其他子任务

			LOG_DEBUG("findSiblingSubTasks 简化实现，返回空列表");
			return siblings;
		}

		std::map<ObjectID, NSUav::UavState> MissionPlanner::getCurrentUavStates() const {
			std::map<ObjectID, NSUav::UavState> states;

			auto available_uavs = getAvailableUAVs();
			for (const auto& uav : available_uavs) {
				if (uav) {
					states[uav->getId()] = uav->getUavState();
				}
			}

			return states;
		}

		NSUav::UavPtr MissionPlanner::getUavById(const ObjectID& uav_id) const {
			auto available_uavs = getAvailableUAVs();
			for (const auto& uav : available_uavs) {
				if (uav && uav->getId() == uav_id) {
					return uav;
				}
			}
			return nullptr;
		}

		const NSMission::Task* MissionPlanner::getTaskById(const ObjectID& task_id) const {
			// 由于我们不使用缓存，这个方法主要用于向后兼容
			// 实际使用中应该使用 findTaskInMission 方法
			LOG_WARN("getTaskById 方法已废弃，请使用 findTaskInMission 方法");
			return nullptr;
		}

		const NSMission::Task* MissionPlanner::findTaskInMission(
			const ObjectID& task_id, const NSMission::Mission& mission) const {

			for (const auto& task_ptr : mission.getTasks()) {
				if (task_ptr && task_ptr->getId() == task_id) {
					return task_ptr.get();
				}
			}

			LOG_WARN("未在Mission中找到任务 ID: {}", task_id);
			return nullptr;
		}



		PlanningResult MissionPlanner::planSingleTask(const NSMission::Task& task) {
			LOG_INFO("开始规划单个任务，任务 ID: {}, 类型: {}", task.getId(), NSUtils::enumToString(task.getType()));

			PlanningResult result;
			result.success = true;
			result.message = "单任务规划初始化";

			// 创建临时Mission包含单个任务
			NSMission::Mission temp_mission;
			temp_mission.addTask(std::make_shared<NSMission::Task>(task));

			// 使用完整的规划流程
			result = planMission(temp_mission);

			if (result.success) {
				LOG_INFO("单个任务 {} 规划成功", task.getId());
			} else {
				LOG_WARN("单个任务 {} 规划失败: {}", task.getId(), result.message);
			}

			return result;
		}
	} // namespace NSPlanning
} // namespace NSDrones