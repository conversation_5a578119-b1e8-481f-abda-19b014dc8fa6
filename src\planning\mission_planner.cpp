// src/planning/mission_planner.cpp
#include "core/types.h"
#include "planning/mission_planner.h"
#include "environment/environment.h"
#include "mission/mission.h"
#include "mission/task.h"
#include "uav/uav.h"
#include "algorithm/allocator/itask_allocator.h"
#include "algorithm/allocator/task_allocator.h"
#include "planning/itask_planner.h"
#include "algorithm/evaluator/itrajectory_evaluator.h"
#include "utils/geometry_manager.h"
#include "utils/logging.h"
#include "utils/enum_utils.h"
#include "params/parameters.h"
#include "params/paramregistry.h"
#include <spdlog/fmt/format.h>
#include <thread>
#include <future>
#include <vector>
#include <map>
#include <set>
#include <algorithm>
#include <numeric>
#include <cmath>
#include <chrono>
#include <stdexcept>
#include <typeinfo>

namespace NSDrones {
	namespace NSPlanning {

		// === 构造函数实现 ===

		MissionPlanner::MissionPlanner() {
			LOG_INFO("任务规划器: MissionPlanner初始化完成");
		}

		
		void MissionPlanner::loadParams() {
			// 暂时没有 MissionPlanner 特有的参数需要加载
			LOG_DEBUG("MissionPlanner::loadParams - 当前无特定参数加载。");
		}

		// === 环境和算法组件访问实现 ===

		std::shared_ptr<NSEnvironment::Environment> MissionPlanner::getEnvironment() const {
			return NSEnvironment::Environment::getInstance();
		}

		IPathPlannerPtr MissionPlanner::getPathPlanner() const {
			auto environment = getEnvironment();
			if (!environment) {
				LOG_ERROR("任务规划器: 无法获取环境实例");
				return nullptr;
			}
			return environment->getPathPlanner();
		}

		ITrajectoryOptimizerPtr MissionPlanner::getTrajectoryOptimizer() const {
			auto environment = getEnvironment();
			if (!environment) {
				LOG_ERROR("任务规划器: 无法获取环境实例");
				return nullptr;
			}
			return environment->getTrajectoryOptimizer();
		}

		ITrajectoryEvaluatorPtr MissionPlanner::getTrajectoryEvaluator() const {
			auto environment = getEnvironment();
			if (!environment) {
				LOG_ERROR("任务规划器: 无法获取环境实例");
				return nullptr;
			}
			return environment->getTrajectoryEvaluator();
		}

		ITaskAllocatorPtr MissionPlanner::getTaskAllocator() const {
			auto environment = getEnvironment();
			if (!environment) {
				LOG_ERROR("任务规划器: 无法获取环境实例");
				return nullptr;
			}
			return environment->getTaskAllocator();
		}

		// === 规划器注册管理实现 ===

		void MissionPlanner::registerTaskPlanner(NSCore::TaskType task_type, TaskPlannerPtr planner) {
			if (planner) {
				std::string type_str = NSUtils::enumToString(task_type);
				if (task_planners_.count(task_type)) {
					LOG_WARN("任务规划器: 覆盖任务类型[{}]的规划器", type_str);
				}
				task_planners_[task_type] = planner;
				LOG_INFO("任务规划器: 已注册任务类型[{}]的规划器", type_str);
			} else {
				LOG_WARN("任务规划器: 尝试注册空的规划器，任务类型[{}]，已忽略",
					NSUtils::enumToString(task_type));
			}
		}

		// === 无人机资源管理实现 ===

		std::vector<NSUav::UavPtr> MissionPlanner::getAvailableUAVs() const {
			std::lock_guard<std::mutex> lock(uav_list_mutex_);
			return available_uavs_; // 返回列表副本
		}

		void MissionPlanner::addAvailableUAV(NSUav::UavPtr uav) {
			if (!uav) {
				LOG_WARN("任务规划器: 尝试添加空的UAV指针，已忽略");
				return;
			}

			const NSUtils::ObjectID& uav_id = uav->getId();
			std::lock_guard<std::mutex> lock(uav_list_mutex_);

			// 检查是否已存在
			bool found = std::any_of(available_uavs_.begin(), available_uavs_.end(),
				[&uav_id](const NSUav::UavPtr& existing_uav) {
					return existing_uav && existing_uav->getId() == uav_id;
				});

			if (!found) {
				available_uavs_.push_back(uav);
				LOG_INFO("任务规划器: 已添加可用无人机{}，当前总数{}", uav_id, available_uavs_.size());
			} else {
				LOG_DEBUG("任务规划器: 无人机{}已存在，忽略添加请求", uav_id);
			}
		}

		bool MissionPlanner::removeAvailableUAV(const NSUtils::ObjectID& uav_id) {
			if (!NSUtils::isValidObjectID(uav_id)) {
				LOG_WARN("任务规划器: 尝试移除无效的UAV ID '{}'，已忽略", uav_id);
				return false;
			}

			std::lock_guard<std::mutex> lock(uav_list_mutex_);

			// 使用remove_if + erase删除匹配的无人机
			auto new_end = std::remove_if(available_uavs_.begin(), available_uavs_.end(),
				[&uav_id](const NSUav::UavPtr& uav) {
					return uav && uav->getId() == uav_id;
				});

			if (new_end != available_uavs_.end()) {
				available_uavs_.erase(new_end, available_uavs_.end());
				LOG_INFO("任务规划器: 已移除可用无人机{}，当前总数{}", uav_id, available_uavs_.size());
				return true;
			} else {
				LOG_WARN("任务规划器: 未找到要移除的无人机ID: {}", uav_id);
				return false;
			}
		}

		// === 规划器查找实现 ===

		TaskPlannerPtr MissionPlanner::findPlannerForTask(const NSMission::Task& task) const {
			auto it = task_planners_.find(task.getType());
			if (it != task_planners_.end()) {
				return it->second;
			} else {
				LOG_WARN("任务规划器: 未找到任务类型[{}]的注册规划器",
					NSUtils::enumToString(task.getType()));
				return nullptr;
			}
		}

		// === 核心规划接口实现（重构后的三阶段流程） ===

		PlanningResult MissionPlanner::planMission(const NSMission::Mission& mission) {
			LOG_INFO("任务规划器: 开始规划任务计划，ID: {}", mission.getId());

			PlanningResult result;
			result.success = true;
			result.message = "规划初始化";

			// === 输入验证 ===
			if (mission.getId().empty()) {
				result.success = false;
				result.message = "任务计划ID为空";
				LOG_ERROR("任务规划器: {}", result.message);
				return result;
			}

			auto tasks = mission.getTasks();
			if (tasks.empty()) {
				result.success = true;
				result.message = "任务计划为空，无需规划";
				LOG_INFO("任务规划器: {}", result.message);
				return result;
			}

			// 验证任务有效性
			for (const auto& task_ptr : tasks) {
				if (!task_ptr) {
					result.success = false;
					result.message = "任务计划包含空任务指针";
					LOG_ERROR("任务规划器: {}", result.message);
					return result;
				}

				if (task_ptr->getId().empty()) {
					result.success = false;
					result.message = "任务计划包含ID为空的任务";
					LOG_ERROR("任务规划器: {}", result.message);
					return result;
				}
			}

			// 获取可用无人机
			std::vector<NSUav::UavPtr> available_uavs = getAvailableUAVs();
			LOG_INFO("任务规划器: 当前可用无人机数量: {}, 任务数量: {}",
				available_uavs.size(), tasks.size());

			if (available_uavs.empty()) {
				result.success = false;
				result.message = "无可用无人机执行任务";
				LOG_ERROR("任务规划器: {}", result.message);
				return result;
			}

			// === 第1步：任务分解和分配阶段 ===
			LOG_INFO("=== 阶段1：任务分解和分配 ===");
			auto allocator = getTaskAllocator();
			if (!allocator) {
				result.success = false;
				result.message = "任务分配器未配置";
				LOG_ERROR("任务规划器: {}", result.message);
				return result;
			}

			// 注意：ITaskAllocator 必须正确设置 SubTaskTarget 的以下字段：
			// - task_type: 从父任务继承的明确任务类型
			// - parent_task_ref: 父任务对象的引用
			// 这样可以避免在后续步骤中进行类型推断
			auto mission_decomposition = allocator->decomposeAndAllocateMission(
				mission, available_uavs, getCurrentUavStates());

			if (!mission_decomposition.success) {
				result.success = false;
				result.message = "任务分解失败: " + mission_decomposition.message;
				LOG_ERROR("任务规划器: {}", result.message);
				return result;
			}

			result.decomposition = mission_decomposition;
			LOG_INFO("任务分解完成，共 {} 个任务被分解", mission_decomposition.task_decompositions.size());

			// === 第2步：单机单任务规划阶段 ===
			LOG_INFO("=== 阶段2：单机单任务规划 ===");

			int total_sub_tasks = 0;
			int successful_sub_tasks = 0;
			int failed_sub_tasks = 0;

			for (const auto& task_decomp : mission_decomposition.task_decompositions) {
				for (const auto& sub_assignment : task_decomp.sub_assignments) {
					total_sub_tasks++;

					// 找到合适的TaskPlanner
					auto planner = findPlannerForSubTask(sub_assignment.sub_target);
					if (!planner) {
						LOG_WARN("未找到子任务 {} 的规划器，跳过", sub_assignment.sub_task_id);
						failed_sub_tasks++;

						// 创建失败的结果记录
						SingleTaskPlanningResult failed_result;
						failed_result.success = false;
						failed_result.sub_task_id = sub_assignment.sub_task_id;
						failed_result.uav_id = sub_assignment.assigned_uav_id;
						failed_result.message = "未找到合适的规划器";
						result.task_results.push_back(failed_result);
						continue;
					}

					// 执行单机规划
					auto single_result = planSingleTaskWithAssignment(mission, sub_assignment);

					// 收集结果
					result.task_results.push_back(single_result);

					if (!single_result.success) {
						LOG_WARN("子任务 {} 规划失败: {}",
							single_result.sub_task_id, single_result.message);
						failed_sub_tasks++;

						// 添加失败信息到全局消息
						if (!result.message.empty()) result.message += "; ";
						result.message += "子任务 " + single_result.sub_task_id + " 规划失败";
					} else {
						LOG_DEBUG("子任务 {} 规划成功，生成 {} 个轨迹点",
							single_result.sub_task_id, single_result.trajectory.getPoints().size());
						successful_sub_tasks++;
					}
				}
			}

			// 评估整体成功状态
			if (successful_sub_tasks == 0 && total_sub_tasks > 0) {
				// 所有子任务都失败
				result.success = false;
				result.message = fmt::format("所有 {} 个子任务规划都失败", total_sub_tasks);
				LOG_ERROR("任务规划器: {}", result.message);
			} else if (failed_sub_tasks > 0) {
				// 部分成功
				result.success = true; // 允许部分成功
				std::string summary = fmt::format("部分成功：{}/{} 个子任务规划成功",
					successful_sub_tasks, total_sub_tasks);
				if (result.message.empty()) {
					result.message = summary;
				} else {
					result.message = summary + "; " + result.message;
				}
				LOG_WARN("任务规划器: {}", summary);
			} else {
				// 全部成功
				result.success = true;
				result.message = fmt::format("全部成功：{} 个子任务规划完成", successful_sub_tasks);
				LOG_INFO("任务规划器: {}", result.message);
			}

			// === 第3步：多机协调优化阶段 ===
			LOG_INFO("=== 阶段3：多机协调优化 ===");
			if (result.success && successful_sub_tasks > 0) {
				// 只有在有成功的子任务时才进行协调优化
				optimizeMultiUavCoordination(result);
				evaluateGlobalPerformance(result);
			} else if (successful_sub_tasks == 0) {
				LOG_WARN("没有成功的子任务，跳过多机协调优化");
			} else {
				LOG_WARN("整体规划失败，跳过多机协调优化");
			}

			LOG_INFO("任务规划完成，任务计划 ID: {}. 最终成功: {}. 生成 {} 个子任务结果, {} 条全局告警。",
				mission.getId(), result.success, result.task_results.size(), result.global_warnings.size());

			return result;
		}

		// === 新的辅助方法实现 ===

		SingleTaskPlanningResult MissionPlanner::planSingleTaskWithAssignment(
			const NSMission::Mission& mission,
			const SubTaskAssignment& assignment) {

			LOG_DEBUG("开始规划子任务：{}, UAV: {}, 任务类型: {}",
				assignment.sub_task_id, assignment.assigned_uav_id,
				NSUtils::enumToString(assignment.getTaskType()));

			SingleTaskPlanningResult result;
			result.success = false;
			result.sub_task_id = assignment.sub_task_id;
			result.uav_id = assignment.assigned_uav_id;

			try {
				// 第1步：验证输入数据
				if (assignment.getTaskType() == NSCore::TaskType::UNKNOWN) {
					result.message = "子任务类型未知";
					LOG_ERROR("子任务 {} 的任务类型为UNKNOWN", assignment.sub_task_id);
					return result;
				}

				// 第2步：构建规划请求
				auto request = buildSingleTaskRequest(assignment, mission);
				if (!request.uav) {
					result.message = "未找到分配的无人机";
					LOG_ERROR("子任务 {} 分配的无人机 {} 不存在",
						assignment.sub_task_id, assignment.assigned_uav_id);
					return result;
				}

				if (!request.original_task) {
					result.message = "未找到原始任务";
					LOG_ERROR("子任务 {} 的原始任务 {} 不存在",
						assignment.sub_task_id, assignment.sub_target.parent_task_id);
					return result;
				}

				// 第3步：找到合适的规划器
				auto planner = findPlannerForSubTask(assignment.sub_target);
				if (!planner) {
					result.message = "未找到合适的规划器";
					LOG_WARN("子任务 {} (类型: {}) 未找到合适的规划器",
						assignment.sub_task_id, NSUtils::enumToString(assignment.getTaskType()));
					return result;
				}

				LOG_DEBUG("使用规划器执行子任务 {} 的规划", assignment.sub_task_id);

				// 第4步：执行规划
				result = planner->planSingleTask(request);

				// 第5步：验证规划结果
				if (result.success) {
					if (result.trajectory.isEmpty()) {
						result.success = false;
						result.message = "规划成功但轨迹为空";
						LOG_WARN("子任务 {} 规划成功但生成的轨迹为空", assignment.sub_task_id);
					} else {
						LOG_INFO("子任务 {} 规划成功，生成 {} 个轨迹点",
							assignment.sub_task_id, result.trajectory.getPoints().size());
					}
				} else {
					LOG_WARN("子任务 {} 规划失败: {}", assignment.sub_task_id, result.message);
				}

			} catch (const std::exception& e) {
				result.message = "规划异常: " + std::string(e.what());
				LOG_ERROR("子任务 {} 规划异常: {}", assignment.sub_task_id, e.what());
			}

			return result;
		}

		void MissionPlanner::optimizeMultiUavCoordination(PlanningResult& result) {
			LOG_INFO("开始多机协调优化");

			if (result.task_results.size() <= 1) {
				LOG_DEBUG("只有一个或零个任务结果，无需多机协调");
				return;
			}

			// 第1步：检测轨迹冲突
			auto conflicts = detectTrajectoryConflicts(result);
			if (conflicts.empty()) {
				LOG_DEBUG("未检测到轨迹冲突");
				return;
			}

			LOG_INFO("检测到 {} 个轨迹冲突，开始解决", conflicts.size());

			// 第2步：解决冲突
			for (const auto& conflict : conflicts) {
				if (!resolveTrajectoryConflict(conflict, result)) {
					LOG_WARN("无法解决UAV {} 和 {} 之间的冲突",
						conflict.uav1_id, conflict.uav2_id);

					// 添加警告
					WarningEvent warning;
					warning.wtype = WarningType::PROXIMITY_ALERT;
					warning.description = fmt::format("UAV {} 和 {} 轨迹存在冲突",
						conflict.uav1_id, conflict.uav2_id);
					warning.time_stamp = conflict.conflict_time;
					warning.location = conflict.conflict_location;
					result.global_warnings.push_back(warning);
				}
			}

			// 第3步：验证协调结果
			auto remaining_conflicts = detectTrajectoryConflicts(result);
			if (remaining_conflicts.empty()) {
				LOG_INFO("多机协调优化完成，所有冲突已解决");
			} else {
				LOG_WARN("多机协调优化完成，仍有 {} 个未解决的冲突", remaining_conflicts.size());
			}
		}

		std::vector<MissionPlanner::TrajectoryConflict> MissionPlanner::detectTrajectoryConflicts(
			const PlanningResult& result) const {

			std::vector<TrajectoryConflict> conflicts;

			// 获取所有UAV的完整轨迹
			auto all_trajectories = result.getAllCompleteTrajectories();

			// 两两比较轨迹
			for (auto it1 = all_trajectories.begin(); it1 != all_trajectories.end(); ++it1) {
				for (auto it2 = std::next(it1); it2 != all_trajectories.end(); ++it2) {

					const auto& uav1_id = it1->first;
					const auto& uav2_id = it2->first;
					const auto& traj1 = it1->second;
					const auto& traj2 = it2->second;

					// 检测两条轨迹的冲突
					auto trajectory_conflicts = detectConflictBetweenTrajectories(
						uav1_id, traj1, uav2_id, traj2);

					conflicts.insert(conflicts.end(),
						trajectory_conflicts.begin(), trajectory_conflicts.end());
				}
			}

			return conflicts;
		}

		bool MissionPlanner::resolveTrajectoryConflict(
			const TrajectoryConflict& conflict, PlanningResult& result) const {

			LOG_INFO("尝试解决UAV {} 和 {} 在时间 {:.2f}s 的冲突",
				conflict.uav1_id, conflict.uav2_id, conflict.conflict_time);

			// === 主要策略：高度错层分离 ===
			if (tryAltitudeSeparationResolution(conflict, result)) {
				LOG_DEBUG("通过高度错层解决了冲突");
				return true;
			}

			// === 备用策略：路径重规划 ===
			if (tryPathReplanning(conflict, result)) {
				LOG_DEBUG("通过路径重规划解决了冲突");
				return true;
			}

			LOG_WARN("无法解决冲突，已尝试所有策略（高度错层、路径重规划）");
			return false;
		}

		std::vector<MissionPlanner::TrajectoryConflict> MissionPlanner::detectConflictBetweenTrajectories(
			const ObjectID& uav1_id, const Trajectory& traj1,
			const ObjectID& uav2_id, const Trajectory& traj2) const {

			std::vector<TrajectoryConflict> conflicts;

			const auto& points1 = traj1.getPoints();
			const auto& points2 = traj2.getPoints();

			if (points1.empty() || points2.empty()) {
				return conflicts;
			}

			LOG_DEBUG("检测UAV {} ({} 点) 和 UAV {} ({} 点) 之间的冲突",
				uav1_id, points1.size(), uav2_id, points2.size());

			// 优化：使用采样间隔减少计算量
			size_t sample_interval = Constants::TRAJECTORY_SAMPLE_INTERVAL;
			size_t j_start = 0;

			// 遍历轨迹1的采样点
			for (size_t i = 0; i < points1.size() && conflicts.size() < Constants::MAX_CONFLICTS_PER_PAIR;
				 i += sample_interval) {

				const auto& p1 = points1[i];

				// 在轨迹2中查找时间窗口内的点
				bool found_in_window = false;
				for (size_t j = j_start; j < points2.size(); j += sample_interval) {
					const auto& p2 = points2[j];

					// 检查时间窗口
					double time_diff = p2.time_stamp - p1.time_stamp;
					if (time_diff > Constants::TIME_TOLERANCE) {
						break; // 后续点时间更大，不需要继续检查
					}

					if (time_diff < -Constants::TIME_TOLERANCE) {
						j_start = j + sample_interval; // 更新搜索起始位置
						continue; // 这个点时间太早，继续下一个
					}

					// 时间在容差范围内，进行冲突检测
					found_in_window = true;

					// === 主要策略：高度错层检测 ===
					double altitude_diff = std::abs(p1.position.altitude - p2.position.altitude);
					double horizontal_distance = GeometryManager::calculateDistance(p1.position, p2.position);

					bool has_conflict = false;
					std::string conflict_type;

					// 检查垂直分离是否足够
					if (altitude_diff < Constants::MIN_VERTICAL_SEPARATION) {
						// 垂直分离不足，检查水平分离
						if (horizontal_distance < Constants::MIN_HORIZONTAL_SEPARATION) {
							has_conflict = true;
							conflict_type = "垂直和水平分离都不足";
						} else {
							// 水平分离足够，但垂直分离不足，仍然记录为潜在冲突
							has_conflict = true;
							conflict_type = "垂直分离不足";
						}
					} else {
						// 垂直分离足够，检查是否在同一高度层但水平距离过近
						if (altitude_diff < Constants::ALTITUDE_SEPARATION_LAYER &&
							horizontal_distance < Constants::SAFE_HORIZONTAL_DISTANCE) {
							has_conflict = true;
							conflict_type = "同一高度层内水平距离过近";
						}
					}

					if (has_conflict) {
						TrajectoryConflict conflict;
						conflict.uav1_id = uav1_id;
						conflict.uav2_id = uav2_id;
						conflict.conflict_time = p1.time_stamp;
						conflict.conflict_location = p1.position;
						conflict.min_distance = std::min(altitude_diff, horizontal_distance);
						conflict.safe_distance = Constants::MIN_VERTICAL_SEPARATION;

						conflicts.push_back(conflict);

						LOG_DEBUG("检测到冲突：UAV {} 和 {} 在时间 {:.2f}s，{}，垂直距离 {:.2f}m，水平距离 {:.2f}m",
							uav1_id, uav2_id, conflict.conflict_time, conflict_type, altitude_diff, horizontal_distance);
					}
				}

				// 性能监控
				if (!found_in_window && i % (sample_interval * 20) == 0) {
					LOG_TRACE("UAV {} 在时间 {:.2f}s 附近未找到UAV {} 的对应点",
						uav1_id, p1.time_stamp, uav2_id);
				}
			}

			if (conflicts.size() >= Constants::MAX_CONFLICTS_PER_PAIR) {
				LOG_WARN("UAV {} 和 {} 之间的冲突数量达到上限 {}，可能存在更多冲突",
					uav1_id, uav2_id, Constants::MAX_CONFLICTS_PER_PAIR);
			}

			return conflicts;
		}



		bool MissionPlanner::tryAltitudeSeparationResolution(
			const TrajectoryConflict& conflict, PlanningResult& result) const {

			// === 智能高度错层分配策略 ===

			// 第1步：分析当前高度使用情况
			std::map<ObjectID, double> current_altitudes;
			std::set<double> used_altitude_layers;

			for (const auto& task_result : result.task_results) {
				if (!task_result.trajectory.isEmpty()) {
					const auto& points = task_result.trajectory.getPoints();
					if (!points.empty()) {
						// 使用轨迹的平均高度作为该UAV的工作高度
						double avg_altitude = 0.0;
						for (const auto& point : points) {
							avg_altitude += point.position.altitude;
						}
						avg_altitude /= points.size();
						current_altitudes[task_result.uav_id] = avg_altitude;

						// 记录已使用的高度层
						int layer = static_cast<int>(std::round(avg_altitude / Constants::ALTITUDE_SEPARATION_LAYER));
						used_altitude_layers.insert(layer * Constants::ALTITUDE_SEPARATION_LAYER);
					}
				}
			}

			// 第2步：为冲突的UAV分配新的高度层
			ObjectID uav_to_adjust;
			double new_altitude;

			// 选择调整优先级较低的UAV（ID较大的，或者高度较高的）
			double alt1 = current_altitudes[conflict.uav1_id];
			double alt2 = current_altitudes[conflict.uav2_id];

			if (alt1 > alt2) {
				uav_to_adjust = conflict.uav1_id;
			} else if (alt2 > alt1) {
				uav_to_adjust = conflict.uav2_id;
			} else {
				// 高度相同，选择ID较大的
				uav_to_adjust = (conflict.uav1_id > conflict.uav2_id) ? conflict.uav1_id : conflict.uav2_id;
			}

			// 第3步：寻找可用的高度层
			double base_altitude = current_altitudes[uav_to_adjust];
			bool found_layer = false;

			// 优先向上寻找空闲层
			for (int layer_offset = 1; layer_offset <= Constants::MAX_ALTITUDE_LAYERS; ++layer_offset) {
				double candidate_altitude = base_altitude + (layer_offset * Constants::ALTITUDE_SEPARATION_LAYER);

				// 检查这个高度层是否被占用
				bool layer_free = true;
				for (double used_alt : used_altitude_layers) {
					if (std::abs(candidate_altitude - used_alt) < Constants::MIN_VERTICAL_SEPARATION) {
						layer_free = false;
						break;
					}
				}

				if (layer_free) {
					new_altitude = candidate_altitude;
					found_layer = true;
					break;
				}
			}

			// 如果向上没找到，向下寻找
			if (!found_layer) {
				for (int layer_offset = 1; layer_offset <= Constants::MAX_ALTITUDE_LAYERS; ++layer_offset) {
					double candidate_altitude = base_altitude - (layer_offset * Constants::ALTITUDE_SEPARATION_LAYER);

					// 确保不会飞得太低
					if (candidate_altitude < 50.0) continue; // 最低飞行高度50米

					bool layer_free = true;
					for (double used_alt : used_altitude_layers) {
						if (std::abs(candidate_altitude - used_alt) < Constants::MIN_VERTICAL_SEPARATION) {
							layer_free = false;
							break;
						}
					}

					if (layer_free) {
						new_altitude = candidate_altitude;
						found_layer = true;
						break;
					}
				}
			}

			if (!found_layer) {
				LOG_WARN("无法找到可用的高度层来解决UAV {} 和 {} 的冲突",
					conflict.uav1_id, conflict.uav2_id);
				return false;
			}

			// 第4步：应用高度调整
			double altitude_adjustment = new_altitude - current_altitudes[uav_to_adjust];

			for (auto& task_result : result.task_results) {
				if (task_result.uav_id == uav_to_adjust) {
					auto& points = task_result.trajectory.getPoints();
					for (auto& point : points) {
						point.position.altitude += altitude_adjustment;
					}

					LOG_INFO("对UAV {} 应用高度错层：从 {:.1f}m 调整到 {:.1f}m（调整 {:.1f}m）",
						uav_to_adjust, current_altitudes[uav_to_adjust], new_altitude, altitude_adjustment);
					return true;
				}
			}

			return false;
		}

		bool MissionPlanner::tryPathReplanning(
			const TrajectoryConflict& conflict, PlanningResult& result) const {

			LOG_INFO("尝试通过路径重规划解决UAV {} 和 {} 的冲突",
				conflict.uav1_id, conflict.uav2_id);

			// === 第1步：选择要重规划的UAV ===
			ObjectID uav_to_replan = selectUavForReplanning(conflict, result);
			if (uav_to_replan.empty()) {
				LOG_WARN("无法选择合适的UAV进行路径重规划");
				return false;
			}

			// === 第2步：获取原始任务信息 ===
			auto original_assignment = findOriginalAssignment(uav_to_replan, result);
			if (!original_assignment) {
				LOG_WARN("无法找到UAV {} 的原始任务分配信息", uav_to_replan);
				return false;
			}

			// === 第3步：构建避障约束 ===
			auto avoidance_constraints = buildAvoidanceConstraints(conflict, result, uav_to_replan);

			// === 第4步：调用任务规划器重新规划 ===
			auto replanned_result = replanWithConstraints(*original_assignment, avoidance_constraints);
			if (!replanned_result.success) {
				LOG_WARN("UAV {} 的路径重规划失败: {}", uav_to_replan, replanned_result.message);
				return false;
			}

			// === 第5步：更新轨迹 ===
			if (!updateTrajectoryInResult(uav_to_replan, replanned_result.trajectory, result)) {
				LOG_WARN("无法更新UAV {} 的轨迹", uav_to_replan);
				return false;
			}

			LOG_INFO("UAV {} 的路径重规划成功，生成新轨迹包含 {} 个点",
				uav_to_replan, replanned_result.trajectory.getPoints().size());
			return true;
		}

		// === 路径重规划辅助方法实现 ===

		ObjectID MissionPlanner::selectUavForReplanning(
			const TrajectoryConflict& conflict, const PlanningResult& result) const {

			// 策略：选择优先级较低的UAV进行重规划
			// 优先级判断标准：
			// 1. 任务复杂度较低的UAV
			// 2. 轨迹较短的UAV
			// 3. ID较大的UAV（作为最后的判断标准）

			auto uav1_task_result = findTaskResultByUav(conflict.uav1_id, result);
			auto uav2_task_result = findTaskResultByUav(conflict.uav2_id, result);

			if (!uav1_task_result || !uav2_task_result) {
				LOG_WARN("无法找到冲突UAV的任务结果");
				return "";
			}

			// 比较轨迹复杂度（轨迹点数量）
			size_t uav1_points = uav1_task_result->trajectory.getPoints().size();
			size_t uav2_points = uav2_task_result->trajectory.getPoints().size();

			if (uav1_points < uav2_points) {
				LOG_DEBUG("选择UAV {} 进行重规划（轨迹点较少：{} vs {}）",
					conflict.uav1_id, uav1_points, uav2_points);
				return conflict.uav1_id;
			} else if (uav2_points < uav1_points) {
				LOG_DEBUG("选择UAV {} 进行重规划（轨迹点较少：{} vs {}）",
					conflict.uav2_id, uav2_points, uav1_points);
				return conflict.uav2_id;
			} else {
				// 轨迹复杂度相同，选择ID较大的
				ObjectID selected = (conflict.uav1_id > conflict.uav2_id) ? conflict.uav1_id : conflict.uav2_id;
				LOG_DEBUG("选择UAV {} 进行重规划（ID较大）", selected);
				return selected;
			}
		}

		const SubTaskAssignment* MissionPlanner::findOriginalAssignment(
			const ObjectID& uav_id, const PlanningResult& result) const {

			// 从规划结果的分解信息中查找原始分配
			if (!result.decomposition.success) {
				LOG_WARN("规划结果中没有有效的分解信息");
				return nullptr;
			}

			for (const auto& task_decomp : result.decomposition.task_decompositions) {
				for (const auto& assignment : task_decomp.sub_assignments) {
					if (assignment.assigned_uav_id == uav_id) {
						LOG_DEBUG("找到UAV {} 的原始任务分配：子任务 {}", uav_id, assignment.sub_task_id);
						return &assignment;
					}
				}
			}

			LOG_WARN("未找到UAV {} 的原始任务分配", uav_id);
			return nullptr;
		}

		std::vector<MissionPlanner::AvoidanceConstraint> MissionPlanner::buildAvoidanceConstraints(
			const TrajectoryConflict& conflict,
			const PlanningResult& result,
			const ObjectID& uav_to_replan) const {

			std::vector<AvoidanceConstraint> constraints;

			// 为冲突中的另一个UAV创建避障约束
			ObjectID other_uav = (uav_to_replan == conflict.uav1_id) ? conflict.uav2_id : conflict.uav1_id;

			auto other_task_result = findTaskResultByUav(other_uav, result);
			if (!other_task_result) {
				LOG_WARN("无法找到UAV {} 的任务结果，无法构建避障约束", other_uav);
				return constraints;
			}

			// 创建轨迹避障约束
			AvoidanceConstraint trajectory_constraint;
			trajectory_constraint.type = AvoidanceConstraint::AVOID_TRAJECTORY;
			trajectory_constraint.source_uav_id = other_uav;
			trajectory_constraint.trajectory_to_avoid = other_task_result->trajectory;
			trajectory_constraint.safety_margin = ConflictConstants::MIN_HORIZONTAL_SEPARATION;

			// 设置时间窗口（在冲突时间前后一段时间内避障）
			const double TIME_BUFFER = 30.0; // 冲突前后30秒的缓冲时间
			trajectory_constraint.start_time = std::max(0.0, conflict.conflict_time - TIME_BUFFER);
			trajectory_constraint.end_time = conflict.conflict_time + TIME_BUFFER;

			constraints.push_back(trajectory_constraint);

			LOG_DEBUG("为UAV {} 构建了 {} 个避障约束", uav_to_replan, constraints.size());
			return constraints;
		}

		SingleTaskPlanningResult MissionPlanner::replanWithConstraints(
			const SubTaskAssignment& assignment,
			const std::vector<AvoidanceConstraint>& constraints) const {

			LOG_DEBUG("开始为子任务 {} 进行约束重规划，约束数量：{}",
				assignment.sub_task_id, constraints.size());

			SingleTaskPlanningResult result;
			result.success = false;
			result.sub_task_id = assignment.sub_task_id;
			result.uav_id = assignment.assigned_uav_id;

			try {
				// 找到合适的任务规划器
				auto planner = findPlannerForSubTask(assignment.sub_target);
				if (!planner) {
					result.message = "未找到合适的规划器进行重规划";
					LOG_WARN("子任务 {} 重规划失败：{}", assignment.sub_task_id, result.message);
					return result;
				}

				// 构建带约束的规划请求
				SingleTaskPlanningRequest request;
				request.assignment = assignment;
				request.uav = getUavById(assignment.assigned_uav_id);
				request.original_task = assignment.getParentTask();
				request.avoidance_constraints = constraints; // 添加避障约束

				if (!request.uav) {
					result.message = "未找到分配的无人机";
					LOG_WARN("子任务 {} 重规划失败：{}", assignment.sub_task_id, result.message);
					return result;
				}

				// 执行约束规划（使用标准的planSingleTask方法，约束通过request传递）
				result = planner->planSingleTask(request);

				// 如果标准规划失败且有约束，可以尝试放宽约束重新规划
				if (!result.success && !constraints.empty()) {
					LOG_INFO("带约束的重规划失败，尝试放宽约束重新规划");

					// 创建放宽约束的请求
					SingleTaskPlanningRequest relaxed_request = request;
					relaxed_request.avoidance_constraints.clear(); // 移除所有约束

					result = planner->planSingleTask(relaxed_request);
					if (result.success) {
						result.message += " (已放宽避障约束)";
						LOG_WARN("子任务 {} 在放宽约束后重规划成功", assignment.sub_task_id);
					}
				}

				if (result.success) {
					LOG_INFO("子任务 {} 约束重规划成功，生成 {} 个轨迹点",
						assignment.sub_task_id, result.trajectory.getPoints().size());
				} else {
					LOG_WARN("子任务 {} 约束重规划失败: {}", assignment.sub_task_id, result.message);
				}

			} catch (const std::exception& e) {
				result.message = "重规划异常: " + std::string(e.what());
				LOG_ERROR("子任务 {} 重规划异常: {}", assignment.sub_task_id, e.what());
			}

			return result;
		}

		bool MissionPlanner::updateTrajectoryInResult(
			const ObjectID& uav_id,
			const Trajectory& new_trajectory,
			PlanningResult& result) const {

			// 在任务结果中找到对应的UAV并更新轨迹
			for (auto& task_result : result.task_results) {
				if (task_result.uav_id == uav_id) {
					task_result.trajectory = new_trajectory;
					LOG_DEBUG("成功更新UAV {} 的轨迹，新轨迹包含 {} 个点",
						uav_id, new_trajectory.getPoints().size());
					return true;
				}
			}

			LOG_WARN("未找到UAV {} 的任务结果，无法更新轨迹", uav_id);
			return false;
		}

		// === 辅助查找方法 ===

		const SingleTaskPlanningResult* MissionPlanner::findTaskResultByUav(
			const ObjectID& uav_id, const PlanningResult& result) const {

			for (const auto& task_result : result.task_results) {
				if (task_result.uav_id == uav_id) {
					return &task_result;
				}
			}
			return nullptr;
		}

		void MissionPlanner::evaluateGlobalPerformance(PlanningResult& result) {
			LOG_INFO("开始全局性能评估");

			auto evaluator = getTrajectoryEvaluator();
			if (!evaluator) {
				LOG_WARN("未配置轨迹评估器，跳过全局性能评估");
				return;
			}

			// 为每个UAV的完整轨迹进行评估
			auto complete_trajectories = result.getAllCompleteTrajectories();
			for (const auto& [uav_id, trajectory] : complete_trajectories) {
				auto uav = getUavById(uav_id);
				if (!uav) {
					LOG_WARN("未找到UAV {}，跳过评估", uav_id);
					continue;
				}

				TrajectoryEvaluationRequest eval_request;
				eval_request.trajectory = trajectory;
				eval_request.uav = uav;

				auto metrics = evaluator->evaluate(eval_request);

				// 将评估结果设置到轨迹中
				for (auto& task_result : result.task_results) {
					if (task_result.uav_id == uav_id) {
						task_result.trajectory.setEvaluationMetrics(metrics);
						break;
					}
				}

				LOG_DEBUG("UAV {} 全局性能评估完成，可行性: {}", uav_id, metrics.is_feasible);
			}

			LOG_DEBUG("全局性能评估完成");
		}

		TaskPlannerPtr MissionPlanner::findPlannerForSubTask(const SubTaskTarget& sub_target) const {
			// 直接使用明确的任务类型查找规划器
			NSCore::TaskType task_type = sub_target.task_type;

			if (task_type == NSCore::TaskType::UNKNOWN) {
				LOG_ERROR("子任务 {} 的任务类型为UNKNOWN，无法查找规划器", sub_target.sub_target_id);
				return nullptr;
			}

			// 方法1：直接根据任务类型查找
			auto it = task_planners_.find(task_type);
			if (it != task_planners_.end() && it->second) {
				LOG_DEBUG("根据任务类型 {} 找到规划器", NSUtils::enumToString(task_type));
				return it->second;
			}

			// 方法2：如果直接查找失败，遍历已注册的规划器进行支持性检查
			for (const auto& [registered_type, planner] : task_planners_) {
				if (planner && planner->isSubTaskSupported(sub_target)) {
					LOG_DEBUG("通过支持性检查找到规划器，任务类型: {}", NSUtils::enumToString(registered_type));
					return planner;
				}
			}

			LOG_WARN("未找到支持子任务 {} (任务类型: {}) 的规划器",
				sub_target.sub_target_id, NSUtils::enumToString(task_type));
			return nullptr;
		}

		SingleTaskPlanningRequest MissionPlanner::buildSingleTaskRequest(
			const SubTaskAssignment& assignment,
			const NSMission::Mission& mission) const {

			SingleTaskPlanningRequest request;
			request.assignment = assignment;
			request.uav = getUavById(assignment.assigned_uav_id);

			// 使用新的数据结构：直接从assignment获取原始任务
			request.original_task = assignment.getParentTask();

			// 如果SubTaskTarget中没有父任务引用，则从Mission中查找
			if (!request.original_task) {
				request.original_task = findTaskInMission(assignment.sub_target.parent_task_id, mission);
			}

			if (!request.uav) {
				LOG_WARN("未找到UAV {}，构建请求失败", assignment.assigned_uav_id);
			}

			if (!request.original_task) {
				LOG_WARN("未找到原始任务 {}，构建请求失败", assignment.sub_target.parent_task_id);
			}

			// 查找兄弟子任务（同一父任务的其他子任务）
			request.sibling_sub_tasks = findSiblingSubTasks(assignment, mission);

			LOG_DEBUG("构建单任务规划请求：子任务 {}, UAV {}, 任务类型 {}, 兄弟任务数 {}",
				assignment.sub_task_id, assignment.assigned_uav_id,
				NSUtils::enumToString(assignment.getTaskType()), request.sibling_sub_tasks.size());

			return request;
		}

		std::vector<ObjectID> MissionPlanner::findSiblingSubTasks(
			const SubTaskAssignment& assignment,
			const NSMission::Mission& mission) const {

			std::vector<ObjectID> siblings;

			// TODO: 完整实现需要访问当前的分解结果
			// 这需要在 planMission 方法中传递分解结果，或者将其存储为成员变量
			//
			// 完整实现的逻辑应该是：
			// 1. 从当前的 MissionDecompositionResult 中查找
			// 2. 找到与 assignment.sub_target.parent_task_id 相同的 TaskDecompositionResult
			// 3. 遍历该 TaskDecompositionResult 的所有 sub_assignments
			// 4. 收集除当前 assignment 之外的所有子任务ID
			//
			// 示例代码：
			// for (const auto& task_decomp : current_decomposition.task_decompositions) {
			//     if (task_decomp.original_task_id == assignment.sub_target.parent_task_id) {
			//         for (const auto& sibling : task_decomp.sub_assignments) {
			//             if (sibling.sub_task_id != assignment.sub_task_id) {
			//                 siblings.push_back(sibling.sub_task_id);
			//             }
			//         }
			//         break;
			//     }
			// }

			LOG_DEBUG("findSiblingSubTasks 简化实现，返回空列表。需要传递分解结果以完整实现。");
			return siblings;
		}

		std::map<ObjectID, NSUav::UavState> MissionPlanner::getCurrentUavStates() const {
			std::map<ObjectID, NSUav::UavState> states;

			auto available_uavs = getAvailableUAVs();
			for (const auto& uav : available_uavs) {
				if (uav) {
					states[uav->getId()] = uav->getUavState();
				}
			}

			return states;
		}

		NSUav::UavPtr MissionPlanner::getUavById(const ObjectID& uav_id) const {
			auto available_uavs = getAvailableUAVs();
			for (const auto& uav : available_uavs) {
				if (uav && uav->getId() == uav_id) {
					return uav;
				}
			}
			return nullptr;
		}

		const NSMission::Task* MissionPlanner::getTaskById(const ObjectID& task_id) const {
			// 由于我们不使用缓存，这个方法主要用于向后兼容
			// 实际使用中应该使用 findTaskInMission 方法
			LOG_WARN("getTaskById 方法已废弃，请使用 findTaskInMission 方法");
			return nullptr;
		}

		const NSMission::Task* MissionPlanner::findTaskInMission(
			const ObjectID& task_id, const NSMission::Mission& mission) const {

			for (const auto& task_ptr : mission.getTasks()) {
				if (task_ptr && task_ptr->getId() == task_id) {
					return task_ptr.get();
				}
			}

			LOG_WARN("未在Mission中找到任务 ID: {}", task_id);
			return nullptr;
		}



		PlanningResult MissionPlanner::planSingleTask(const NSMission::Task& task) {
			LOG_INFO("开始规划单个任务，任务 ID: {}, 类型: {}", task.getId(), NSUtils::enumToString(task.getType()));

			PlanningResult result;
			result.success = true;
			result.message = "单任务规划初始化";

			// 创建临时Mission包含单个任务
			NSMission::Mission temp_mission;
			temp_mission.addTask(std::make_shared<NSMission::Task>(task));

			// 使用完整的规划流程
			result = planMission(temp_mission);

			if (result.success) {
				LOG_INFO("单个任务 {} 规划成功", task.getId());
			} else {
				LOG_WARN("单个任务 {} 规划失败: {}", task.getId(), result.message);
			}

			return result;
		}
	} // namespace NSPlanning
} // namespace NSDrones