// include/planning/task_planners/task_planner_followpath.h
#pragma once

#include "planning/itask_planner.h"
#include "mission/task.h"
#include "mission/task_params.h"
#include "planning/planning_types.h"
#include "environment/environment_fwd.h"
#include "algorithm/path_planner/ipath_planner.h"
#include "algorithm/trajectory_optimizer/itrajectory_optimizer.h"
#include "utils/logging.h"

namespace NSDrones {
	namespace NSPlanning {
		using namespace NSDrones::NSUtils;
		using namespace NSDrones::NSCore;

		/**
		 * @class FollowPathTaskPlanner
		 * @brief 负责规划 FOLLOW_PATH 任务 (沿路径点飞行)。
		 *        支持单机和多机独立规划场景。
		 */
		class FollowPathTaskPlanner : public ITaskPlanner {
		public:
			/** @brief 构造函数 */
			FollowPathTaskPlanner();

			/** @brief 检查是否支持 FOLLOW_PATH 类型。 */
			bool isTaskTypeSupported(TaskType task_type) const override {
				return task_type == TaskType::FOLLOW_PATH;
			}

			/**
			 * @brief 规划单机路径跟随任务（重构后的接口）
			 * @param request 单任务规划请求
			 * @return 单任务规划结果
			 */
			SingleTaskPlanningResult planSingleTask(const SingleTaskPlanningRequest& request) override;

			/**
			 * @brief 检查是否支持指定的子任务类型
			 * @param sub_target 子任务目标
			 * @return 是否支持
			 */
			bool isSubTaskSupported(const SubTaskTarget& sub_target) const override;

			/**
			 * @brief 初始化任务规划器。
			 * @param params 包含配置参数的 ParamValues 对象。
			 * @param raw_config 实例的原始JSON配置。
			 * @return 如果初始化成功则返回 true，否则返回 false。
			 */
			bool initialize(std::shared_ptr<NSParams::ParamValues> params, const nlohmann::json& raw_config) override;

		private:
			/**
			 * @brief 规划路径跟随轨迹的核心实现
			 * @param request 单任务规划请求
			 * @param params 路径跟随任务参数
			 * @param start_state 起始状态
			 * @return 单任务规划结果
			 */
			SingleTaskPlanningResult planFollowPathTrajectory(
				const SingleTaskPlanningRequest& request,
				const NSMission::FollowPathTaskParams& params,
				const NSUav::UavState& start_state);

			// 路径跟随参数
			double follow_speed_ = 15.0;           ///< 跟随速度 (米/秒)
			double path_tolerance_ = 2.0;          ///< 路径容差 (米)
			double waypoint_tolerance_ = 1.0;      ///< 航点容差 (米)
			bool smooth_path_ = true;              ///< 是否平滑路径
			std::string interpolation_method_ = "spline"; ///< 插值方法 (linear, spline, bezier)
		};

	} // namespace NSPlanning
} // namespace NSDrones