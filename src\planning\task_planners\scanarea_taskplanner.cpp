// src/planning/task_planners/task_planner_scanarea.cpp
#include "planning/task_planners/scanarea_taskplanner.h"
#include "planning/planning_result.h"
#include "planning/planning_types.h"
#include "mission/task.h"
#include "mission/task_params.h"
#include "mission/task_strategies.h"
#include "uav/uav.h"
#include "environment/environment.h"
#include "algorithm/path_planner/ipath_planner.h"
#include "algorithm/trajectory_optimizer/itrajectory_optimizer.h"
#include "utils/logging.h"
#include "utils/geometry_manager.h"
#include "params/parameters.h"
#include "params/paramregistry.h"
#include <vector>
#include <cmath>
#include <sstream>
#include <iomanip>
#include <algorithm>
#include <utility>

namespace NSDrones {
	namespace NSPlanning {

		ScanAreaTaskPlanner::ScanAreaTaskPlanner()
			: ITaskPlanner() {}

		bool ScanAreaTaskPlanner::initialize(std::shared_ptr<NSParams::ParamValues> params,
											 const nlohmann::json& raw_config) {
			LOG_DEBUG("扫描任务规划器: 开始初始化");

			if (params) {
				// 加载扫描相关参数
				default_scan_altitude_ = params->getValueOrDefault<double>("scan.altitude", 50.0);
				default_scan_speed_ = params->getValueOrDefault<double>("scan.speed", 10.0);
				default_overlap_ratio_ = params->getValueOrDefault<double>("scan.overlap_ratio", 0.3);

				// 加载扫描模式参数
				auto pattern_opt = params->getEnumValue<NSPlanning::ScanPatternType>("task_planner.scan.pattern");
				if (pattern_opt.has_value()) {
					default_scan_pattern_ = pattern_opt.value();
				} else {
					LOG_WARN("扫描任务规划器: 无法获取扫描模式参数，使用默认值ZIGZAG");
					default_scan_pattern_ = NSPlanning::ScanPatternType::ZIGZAG;
				}

				LOG_INFO("扫描任务规划器: 参数加载完成 - 高度:{:.1f}m, 速度:{:.1f}m/s, 重叠率:{:.2f}, 模式:{}",
						default_scan_altitude_, default_scan_speed_, default_overlap_ratio_,
						NSUtils::enumToString(default_scan_pattern_));
			}

			// 加载相机参数
			if (!raw_config.empty() && raw_config.contains("camera_fov")) {
				camera_fov_deg_ = raw_config["camera_fov"].get<double>();
				LOG_DEBUG("扫描任务规划器: 相机视场角设置为{:.1f}度", camera_fov_deg_);
			}

			LOG_DEBUG("扫描任务规划器: 初始化完成");
			return true;
		}

		PlanningResult ScanAreaTaskPlanner::planTask(
			const NSMission::Task& task,
			const std::vector<NSUav::UavPtr>& assigned_uavs,
			const std::map<NSUtils::ObjectID, NSUav::UavState>& start_states) {

			LOG_INFO("扫描任务规划器: 开始规划任务[{}]", task.getId());
			PlanningResult result;
			result.setStatus(true);

			// 1. 基本验证
			if (!validateTaskBasics(task, NSMission::TaskType::SCAN_AREA, assigned_uavs, result)) {
				return result;
			}

			// 2. 获取和验证扫描参数
			auto params_ptr = task.getTaskParameters<NSMission::ScanAreaTaskParams>();
			if (!params_ptr) {
				result.setStatus(false, "无法获取扫描任务参数");
				LOG_ERROR("扫描任务规划器: 任务[{}]参数获取失败", task.getId());
				return result;
			}

			if (!validateScanAreaParams(*params_ptr, task.getId(), result)) {
				return result;
			}

			// 3. 验证边界点共面性
			LOG_DEBUG("扫描任务规划器: 任务[{}]检查边界点共面性", task.getId());
			double max_deviation = 0.0;
			if (!GeometryManager::checkPointsCoplanar(params_ptr->area_boundary, max_deviation)) {
				std::string error_msg = "边界点不共面，最大偏差:" + std::to_string(max_deviation) + "米";
				result.setStatus(false, error_msg);
				LOG_ERROR("扫描任务规划器: 任务[{}] {}", task.getId(), error_msg);
				return result;
			}
			LOG_DEBUG("扫描任务规划器: 任务[{}]边界点共面性检查通过，偏差:{:.4f}米", task.getId(), max_deviation);

			// 4. 生成扫描路径
			LOG_DEBUG("扫描任务规划器: 任务[{}]生成扫描路径", task.getId());
			double height_above_plane = params_ptr->scan_height_above_plane.value_or(0.0);

			std::vector<NSCore::WGS84Point> scan_path_wgs84 = GeometryManager::generateScanPath(
				params_ptr->area_boundary,
				params_ptr->strip_width,
				params_ptr->overlap_ratio,
				params_ptr->scan_angle_deg,
				height_above_plane
			);

			if (scan_path_wgs84.size() < 2) {
				result.setStatus(false, "生成的扫描路径点数不足");
				LOG_ERROR("扫描任务规划器: 任务[{}]扫描路径生成失败", task.getId());
				return result;
			}

			// 转换为ECEF坐标
			std::vector<NSCore::EcefPoint> scan_path_ecef;
			scan_path_ecef.reserve(scan_path_wgs84.size());
			for (const auto& wgs84_point : scan_path_wgs84) {
				scan_path_ecef.push_back(NSUtils::CoordinateConverter::wgs84ToECEF(wgs84_point));
			}

			LOG_INFO("扫描任务规划器: 任务[{}]成功生成{}个扫描路径点", task.getId(), scan_path_ecef.size());

			// 5. 为每个无人机规划路径
			bool overall_success = true;
			for (const auto& uav : assigned_uavs) {
				if (!validateUavState(uav, start_states, task.getId(), result)) {
					overall_success = false;
					continue;
				}

				const NSUtils::ObjectID& uav_id = uav->getId();
				const NSUav::UavState& start_state = start_states.at(uav_id);

				LOG_INFO("扫描任务规划器: 为无人机[{}]规划扫描路径", uav_id);

				if (!planScanPathForUav(uav, scan_path_ecef, start_state, task, result)) {
					overall_success = false;
					continue;
				}
			}

			// 6. 设置最终状态
			if (!overall_success && result.wasSuccessful()) {
				result.setStatus(false, "部分无人机扫描路径规划失败");
			}

			LOG_INFO("扫描任务规划器: 任务[{}]规划完成，状态:{}, 生成{}条航线",
					task.getId(), result.wasSuccessful() ? "成功" : "失败", result.getAllRoutes().size());
			return result;
		}

		// === 私有辅助方法实现 ===

		bool ScanAreaTaskPlanner::validateScanAreaParams(const NSMission::ScanAreaTaskParams& params,
														 const NSUtils::ObjectID& task_id,
														 PlanningResult& result) const {
			// 验证边界点数量
			if (params.area_boundary.size() < 3) {
				std::string error_msg = "扫描区域边界点数(" + std::to_string(params.area_boundary.size()) + ")少于3个";
				result.setStatus(false, error_msg);
				LOG_ERROR("扫描任务规划器: 任务[{}] {}", task_id, error_msg);
				return false;
			}

			// 验证条带宽度
			if (params.strip_width <= NSCore::Constants::GEOMETRY_EPSILON) {
				std::string error_msg = "条带宽度(" + std::to_string(params.strip_width) + ")必须为正值";
				result.setStatus(false, error_msg);
				LOG_ERROR("扫描任务规划器: 任务[{}] {}", task_id, error_msg);
				return false;
			}

			// 验证重叠率
			if (params.overlap_ratio < 0.0 || params.overlap_ratio >= 1.0) {
				std::string error_msg = "重叠率(" + std::to_string(params.overlap_ratio) + ")必须在[0,1)范围内";
				result.setStatus(false, error_msg);
				LOG_ERROR("扫描任务规划器: 任务[{}] {}", task_id, error_msg);
				return false;
			}

			LOG_DEBUG("扫描任务规划器: 任务[{}]参数验证通过 - 边界点:{}, 条带宽度:{:.2f}, 重叠率:{:.2f}, 扫描角度:{:.1f}°",
					 task_id, params.area_boundary.size(), params.strip_width,
					 params.overlap_ratio, params.scan_angle_deg);
			return true;
		}

		bool ScanAreaTaskPlanner::planScanPathForUav(const NSUav::UavPtr& uav,
													 const std::vector<NSCore::EcefPoint>& scan_path_ecef,
													 const NSUav::UavState& start_state,
													 const NSMission::Task& task,
													 PlanningResult& result) {
			const NSUtils::ObjectID& uav_id = uav->getId();

			// 规划进入扫描起点的路径
			NSCore::WGS84Point scan_start_wgs84 = NSUtils::CoordinateConverter::ecefToWGS84(scan_path_ecef.front());
			std::vector<NSCore::EcefPoint> entry_path = planPathToTarget(start_state, scan_start_wgs84, uav, task, result);

			if (entry_path.empty()) {
				LOG_ERROR("扫描任务规划器: 无人机[{}]无法规划进入扫描起点的路径", uav_id);
				return false;
			}

			// 合并进入路径和扫描路径
			std::vector<NSCore::EcefPoint> full_path = entry_path;

			// 移除可能的重合点
			if (!full_path.empty() && !scan_path_ecef.empty()) {
				if ((full_path.back() - scan_path_ecef.front()).norm() < NSCore::Constants::GEOMETRY_EPSILON) {
					full_path.pop_back();
				}
			}

			full_path.insert(full_path.end(), scan_path_ecef.begin(), scan_path_ecef.end());

			// 平滑和时间参数化
			RouteSegment final_segment;
			double desired_speed = task.getDesiredSpeed(default_scan_speed_);

			if (!smoothAndTimeParameterizeECEF(full_path, uav, start_state, desired_speed,
											  final_segment, &result, task.getStrategies())) {
				LOG_ERROR("扫描任务规划器: 无人机[{}]路径平滑和时间参数化失败", uav_id);
				return false;
			}

			// 添加到结果
			if (final_segment.empty()) {
				LOG_WARN("扫描任务规划器: 无人机[{}]生成的航段为空", uav_id);
				return false;
			}

			PlannedRoute route(uav_id);
			route.addWaypoints(final_segment);
			checkSegmentWarnings(route, uav_id, result, task.getId());
			result.addRoute(std::move(route));

			LOG_INFO("扫描任务规划器: 无人机[{}]扫描路径规划成功，生成{}个航点", uav_id, final_segment.size());
			return true;
		}

	} // namespace NSPlanning
} // namespace NSDrones