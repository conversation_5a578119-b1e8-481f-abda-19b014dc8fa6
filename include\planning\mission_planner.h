// include/planning/mission_planner.h
#pragma once

#include "core/types.h"
#include "planning/itask_planner.h"
#include "algorithm/path_planner/ipath_planner.h"
#include "algorithm/trajectory_optimizer/itrajectory_optimizer.h"
#include "algorithm/evaluator/itrajectory_evaluator.h"
#include "algorithm/allocator/itask_allocator.h"
#include "planning/planning_types.h"
#include "mission/mission.h"
#include "uav/uav.h"
#include <vector>
#include <map>
#include <memory>
#include <mutex>
#include <shared_mutex>

// 前向声明
namespace NSDrones {
	namespace NSMission {
		class Task;
		class ControlPoint;
	}
	namespace NSEnvironment {
		class Environment;
		template<typename ObjectContainer> class CollisionEngine;
	}
}

namespace NSDrones {
	namespace NSPlanning {

		/**
		 * @class MissionPlanner
		 * @brief 顶层任务规划协调器
		 *
		 * 作为整个规划系统的核心协调器，负责管理和协调多个子系统的工作。
		 *
		 * ## 核心职责
		 * - **任务分解**: 将Mission分解为多个独立的Task
		 * - **资源分配**: 为每个Task分配合适的无人机
		 * - **规划协调**: 调用相应的TaskPlanner进行具体规划
		 * - **结果整合**: 将各Task的规划结果整合为完整航线
		 * - **质量评估**: 使用评估器验证规划结果质量
		 *
		 * ## 架构设计
		 * - **组合模式**: 组合多个专业规划器和评估器
		 * - **策略模式**: 支持不同的任务分配和评估策略
		 * - **工厂模式**: 根据任务类型选择合适的规划器
		 *
		 * ## 线程安全
		 * - **资源管理**: 无人机列表访问是线程安全的
		 * - **规划过程**: planMission和planSingleTask不是线程安全的，不应并发调用
		 */
		class MissionPlanner {
		public:
			// === 生命周期管理 ===

			/**
			 * @brief 构造函数
			 * @throws DroneException 如果环境未初始化
			 * @note 所有算法组件（路径规划器、轨迹优化器、评估器、分配器）都从Environment中获取
			 */
			MissionPlanner();

			// === 禁止拷贝和移动 ===
			MissionPlanner(const MissionPlanner&) = delete;
			MissionPlanner& operator=(const MissionPlanner&) = delete;
			MissionPlanner(MissionPlanner&&) = delete;
			MissionPlanner& operator=(MissionPlanner&&) = delete;

			// === 规划器注册管理 ===

			/**
			 * @brief 注册特定任务类型的规划器实现
			 * @param task_type 要处理的任务类型
			 * @param planner 能够处理该类型任务的TaskPlanner实例
			 * @note 如果已存在相同类型的规划器，则会覆盖
			 */
			void registerTaskPlanner(NSCore::TaskType task_type, TaskPlannerPtr planner);

			// === 无人机资源管理（线程安全） ===

			/**
			 * @brief 获取所有可用的无人机列表
			 * @return 当前可用无人机列表的副本（线程安全）
			 */
			std::vector<NSUav::UavPtr> getAvailableUAVs() const;

			/**
			 * @brief 添加可用的无人机资源
			 * @param uav 无人机实例的共享指针
			 * @note 如果UAV已存在则忽略，线程安全
			 */
			void addAvailableUAV(NSUav::UavPtr uav);

			/**
			 * @brief 移除无人机资源
			 * @param uav_id 要移除的无人机ID
			 * @return 成功找到并移除返回true，线程安全
			 */
			bool removeAvailableUAV(const NSUtils::ObjectID& uav_id);

			// === 核心规划接口（重构后的工作流程） ===

			/**
			 * @brief 规划整个任务计划（重构后的流程）
			 *
			 * 新的三阶段规划流程：
			 * 1. **任务分解和分配阶段**：使用TaskAllocator将复杂任务分解为子任务并分配UAV
			 * 2. **单机单任务规划阶段**：使用TaskPlanner为每个子任务生成轨迹
			 * 3. **多机协调优化阶段**：处理UAV间的协调约束和全局优化
			 *
			 * @param mission 要规划的任务计划对象
			 * @return 包含所有无人机完整航线和告警的PlanningResult
			 * @warning 此方法非线程安全，不应并发调用
			 */
			PlanningResult planMission(const NSMission::Mission& mission);

			/**
			 * @brief 规划单个任务（重构后的流程）
			 *
			 * 主要用于测试或特殊场景，执行单任务的完整分解和规划流程。
			 *
			 * @param task 要规划的单个任务
			 * @return 包含该任务涉及的无人机航线和告警的PlanningResult
			 * @warning 此方法非线程安全
			 */
			PlanningResult planSingleTask(const NSMission::Task& task);

		private:
			// === 重构后的核心规划流程 ===

			/**
			 * @brief 为已分配的单个任务规划航线
			 * @param mission 完整任务计划（用于获取上下文信息）
			 * @param assignment 子任务分配信息
			 * @param decomposition 任务分解结果（用于查找兄弟子任务）
			 * @return 单任务规划结果
			 */
			SingleTaskPlanningResult planSingleTaskWithAssignment(
				const NSMission::Mission& mission,
				const SubTaskAssignment& assignment,
				const MissionDecompositionResult& decomposition);

			/**
			 * @brief 全局轨迹优化（处理UAV间的协调）
			 * @param result 规划结果（输入输出参数）
			 */
			void optimizeMultiUavCoordination(PlanningResult& result);

			/**
			 * @brief 全局性能评估
			 * @param result 规划结果（输入输出参数）
			 */
			void evaluateGlobalPerformance(PlanningResult& result);

			// === 规划器查找和管理 ===

			/**
			 * @brief 根据子任务目标查找合适的TaskPlanner
			 * @param sub_target 子任务目标
			 * @return 指向合适TaskPlanner的共享指针，找不到则返回nullptr
			 */
			TaskPlannerPtr findPlannerForSubTask(const SubTaskTarget& sub_target) const;

			/**
			 * @brief 构建单任务规划请求
			 * @param assignment 子任务分配
			 * @param mission 完整任务计划
			 * @param decomposition 任务分解结果
			 * @return 单任务规划请求
			 */
			SingleTaskPlanningRequest buildSingleTaskRequest(
				const SubTaskAssignment& assignment,
				const NSMission::Mission& mission,
				const MissionDecompositionResult& decomposition) const;

			// === 参数和配置管理 ===

			/**
			 * @brief 加载配置参数
			 */
			void loadParams();

			// === 辅助方法 ===

			// 注意：convertAllocationResult 方法已不再需要，
			// 因为新的 ITaskAllocator 直接返回 MissionDecompositionResult

			/**
			 * @brief 获取当前UAV状态映射
			 * @return UAV ID到状态的映射
			 */
			std::map<ObjectID, NSUav::UavState> getCurrentUavStates() const;

			/**
			 * @brief 根据ID获取UAV对象
			 * @param uav_id UAV ID
			 * @return UAV对象指针，找不到则返回nullptr
			 */
			NSUav::UavPtr getUavById(const ObjectID& uav_id) const;

			/**
			 * @brief 根据ID获取任务对象
			 * @param task_id 任务ID
			 * @return 任务对象指针，找不到则返回nullptr
			 */
			const NSMission::Task* getTaskById(const ObjectID& task_id) const;

			/**
			 * @brief 在指定Mission中查找任务
			 * @param task_id 任务ID
			 * @param mission 任务计划
			 * @return 任务对象指针，找不到则返回nullptr
			 */
			const NSMission::Task* findTaskInMission(const ObjectID& task_id, const NSMission::Mission& mission) const;

			// === 多机协调相关方法 ===

			/**
			 * @brief 轨迹冲突信息
			 */
			struct TrajectoryConflict {
				ObjectID uav1_id;
				ObjectID uav2_id;
				Time conflict_time;
				WGS84Point conflict_location;
				double min_distance;
				double safe_distance = 10.0; // 默认安全距离10米
			};



			/**
			 * @brief 检测轨迹冲突
			 * @param result 规划结果
			 * @return 冲突列表
			 */
			std::vector<TrajectoryConflict> detectTrajectoryConflicts(const PlanningResult& result) const;

			/**
			 * @brief 解决单个轨迹冲突
			 * @param conflict 冲突信息
			 * @param result 规划结果（输入输出参数）
			 * @return 是否成功解决冲突
			 */
			bool resolveTrajectoryConflict(const TrajectoryConflict& conflict, PlanningResult& result) const;

			/**
			 * @brief 检测两条轨迹之间的冲突
			 */
			std::vector<TrajectoryConflict> detectConflictBetweenTrajectories(
				const ObjectID& uav1_id, const Trajectory& traj1,
				const ObjectID& uav2_id, const Trajectory& traj2) const;

			/**
			 * @brief 尝试通过高度分离解决冲突
			 */
			bool tryAltitudeSeparationResolution(const TrajectoryConflict& conflict, PlanningResult& result) const;

			/**
			 * @brief 尝试通过路径重规划解决冲突
			 */
			bool tryPathReplanning(const TrajectoryConflict& conflict, PlanningResult& result) const;

			// === 路径重规划辅助方法 ===

			/**
			 * @brief 选择需要重规划的UAV
			 * @param conflict 冲突信息
			 * @param result 当前规划结果
			 * @return 选中的UAV ID
			 */
			ObjectID selectUavForReplanning(const TrajectoryConflict& conflict, const PlanningResult& result) const;

			/**
			 * @brief 查找UAV的原始任务分配
			 * @param uav_id UAV ID
			 * @param result 当前规划结果
			 * @return 原始任务分配指针
			 */
			const SubTaskAssignment* findOriginalAssignment(const ObjectID& uav_id, const PlanningResult& result) const;

			/**
			 * @brief 构建避障约束
			 * @param conflict 冲突信息
			 * @param result 当前规划结果
			 * @param uav_to_replan 要重规划的UAV ID
			 * @return 避障约束列表
			 */
			std::vector<AvoidanceConstraint> buildAvoidanceConstraints(
				const TrajectoryConflict& conflict,
				const PlanningResult& result,
				const ObjectID& uav_to_replan) const;

			/**
			 * @brief 使用约束重新规划
			 * @param assignment 原始任务分配
			 * @param constraints 避障约束
			 * @return 重规划结果
			 */
			SingleTaskPlanningResult replanWithConstraints(
				const SubTaskAssignment& assignment,
				const std::vector<AvoidanceConstraint>& constraints) const;

			/**
			 * @brief 更新规划结果中的轨迹
			 * @param uav_id UAV ID
			 * @param new_trajectory 新轨迹
			 * @param result 规划结果（输入输出参数）
			 * @return 是否成功更新
			 */
			bool updateTrajectoryInResult(
				const ObjectID& uav_id,
				const Trajectory& new_trajectory,
				PlanningResult& result) const;

			/**
			 * @brief 根据UAV ID查找任务结果
			 * @param uav_id UAV ID
			 * @param result 规划结果
			 * @return 任务结果指针，找不到则返回nullptr
			 */
			const SingleTaskPlanningResult* findTaskResultByUav(
				const ObjectID& uav_id, const PlanningResult& result) const;

			/**
			 * @brief 查找兄弟子任务
			 * @param assignment 当前子任务分配
			 * @param mission 任务计划
			 * @param decomposition 任务分解结果
			 * @return 兄弟子任务ID列表
			 */
			std::vector<ObjectID> findSiblingSubTasks(
				const SubTaskAssignment& assignment,
				const NSMission::Mission& mission,
				const MissionDecompositionResult& decomposition) const;

			/**
			 * @brief 验证兄弟子任务的一致性
			 * @param assignment 当前子任务分配
			 * @param task_decomp 任务分解结果
			 * @param siblings 兄弟子任务ID列表
			 */
			void validateSiblingConsistency(
				const SubTaskAssignment& assignment,
				const TaskDecompositionResult& task_decomp,
				const std::vector<ObjectID>& siblings) const;

			// === 环境和算法组件访问 ===

			/**
			 * @brief 获取环境实例
			 * @return 环境实例的共享指针
			 */
			std::shared_ptr<NSEnvironment::Environment> getEnvironment() const;

			/**
			 * @brief 从环境获取路径规划器
			 * @return 路径规划器的共享指针，如果未配置则返回nullptr
			 */
			IPathPlannerPtr getPathPlanner() const;

			/**
			 * @brief 从环境获取轨迹优化器
			 * @return 轨迹优化器的共享指针，如果未配置则返回nullptr
			 */
			ITrajectoryOptimizerPtr getTrajectoryOptimizer() const;

			/**
			 * @brief 从环境获取轨迹评估器
			 * @return 轨迹评估器的共享指针，如果未配置则返回nullptr
			 */
			ITrajectoryEvaluatorPtr getTrajectoryEvaluator() const;

			/**
			 * @brief 从环境获取任务分配器
			 * @return 任务分配器的共享指针，如果未配置则返回nullptr
			 */
			ITaskAllocatorPtr getTaskAllocator() const;

			// === 成员变量 ===

			// 内部状态
			std::map<NSCore::TaskType, TaskPlannerPtr> task_planners_;  ///< 任务类型到规划器的映射
			std::vector<NSUav::UavPtr> available_uavs_;                    ///< 可用无人机列表
			mutable std::mutex uav_list_mutex_;                            ///< 保护无人机列表的互斥锁
		};

	} // namespace NSPlanning
} // namespace NSDrones