// include/planning/mission_planner.h
#pragma once

#include "core/types.h"
#include "planning/itask_planner.h"
#include "algorithm/path_planner/ipath_planner.h"
#include "algorithm/trajectory_optimizer/itrajectory_optimizer.h"
#include "algorithm/evaluator/itrajectory_evaluator.h"
#include "algorithm/allocator/itask_allocator.h"
#include "planning/planning_result.h"
#include "mission/mission.h"
#include "uav/uav.h"
#include <vector>
#include <map>
#include <memory>
#include <mutex>
#include <shared_mutex>

// 前向声明
namespace NSDrones {
	namespace NSMission {
		class Task;
		class ControlPoint;
	}
	namespace NSEnvironment {
		class Environment;
		template<typename ObjectContainer> class CollisionEngine;
	}
}

namespace NSDrones {
	namespace NSPlanning {

		/**
		 * @class MissionPlanner
		 * @brief 顶层任务规划协调器
		 *
		 * 作为整个规划系统的核心协调器，负责管理和协调多个子系统的工作。
		 *
		 * ## 核心职责
		 * - **任务分解**: 将Mission分解为多个独立的Task
		 * - **资源分配**: 为每个Task分配合适的无人机
		 * - **规划协调**: 调用相应的TaskPlanner进行具体规划
		 * - **结果整合**: 将各Task的规划结果整合为完整航线
		 * - **质量评估**: 使用评估器验证规划结果质量
		 *
		 * ## 架构设计
		 * - **组合模式**: 组合多个专业规划器和评估器
		 * - **策略模式**: 支持不同的任务分配和评估策略
		 * - **工厂模式**: 根据任务类型选择合适的规划器
		 *
		 * ## 线程安全
		 * - **资源管理**: 无人机列表访问是线程安全的
		 * - **规划过程**: planMission和planSingleTask不是线程安全的，不应并发调用
		 */
		class MissionPlanner {
		public:
			// === 生命周期管理 ===

			/**
			 * @brief 构造函数
			 * @throws DroneException 如果环境未初始化
			 * @note 所有算法组件（路径规划器、轨迹优化器、评估器、分配器）都从Environment中获取
			 */
			MissionPlanner();

			// === 禁止拷贝和移动 ===
			MissionPlanner(const MissionPlanner&) = delete;
			MissionPlanner& operator=(const MissionPlanner&) = delete;
			MissionPlanner(MissionPlanner&&) = delete;
			MissionPlanner& operator=(MissionPlanner&&) = delete;

			// === 规划器注册管理 ===

			/**
			 * @brief 注册特定任务类型的规划器实现
			 * @param task_type 要处理的任务类型
			 * @param planner 能够处理该类型任务的TaskPlanner实例
			 * @note 如果已存在相同类型的规划器，则会覆盖
			 */
			void registerTaskPlanner(NSMission::TaskType task_type, TaskPlannerPtr planner);

			// === 无人机资源管理（线程安全） ===

			/**
			 * @brief 获取所有可用的无人机列表
			 * @return 当前可用无人机列表的副本（线程安全）
			 */
			std::vector<NSUav::UavPtr> getAvailableUAVs() const;

			/**
			 * @brief 添加可用的无人机资源
			 * @param uav 无人机实例的共享指针
			 * @note 如果UAV已存在则忽略，线程安全
			 */
			void addAvailableUAV(NSUav::UavPtr uav);

			/**
			 * @brief 移除无人机资源
			 * @param uav_id 要移除的无人机ID
			 * @return 成功找到并移除返回true，线程安全
			 */
			bool removeAvailableUAV(const NSUtils::ObjectID& uav_id);

			// === 核心规划接口（非线程安全） ===

			/**
			 * @brief 规划整个任务计划
			 *
			 * 执行完整的任务规划流程：
			 * 1. 按顺序处理各个任务
			 * 2. 为每个任务分配合适的无人机
			 * 3. 调用相应的TaskPlanner进行规划
			 * 4. 合并所有结果生成最终航线
			 * 5. 使用评估器验证规划质量（如果配置）
			 *
			 * @param mission 要规划的任务计划对象
			 * @return 包含所有无人机完整航线和告警的PlanningResult
			 * @warning 此方法非线程安全，不应并发调用
			 */
			PlanningResult planMission(const NSMission::Mission& mission);

			/**
			 * @brief 规划单个任务
			 *
			 * 主要用于测试或特殊场景，会从当前可用无人机中选择执行者。
			 * 如果配置了评估器，会对生成的航线进行评估。
			 *
			 * @param task 要规划的单个任务
			 * @return 包含该任务涉及的无人机航线和告警的PlanningResult
			 * @warning 此方法非线程安全
			 */
			PlanningResult planSingleTask(const NSMission::Task& task);



		private:
			// === 规划器查找和管理 ===

			/**
			 * @brief 根据任务类型查找合适的TaskPlanner
			 * @param task 要规划的任务
			 * @return 指向合适TaskPlanner的共享指针，找不到则返回nullptr
			 * @note 此方法是const，不修改内部状态
			 */
			TaskPlannerPtr findPlannerForTask(const NSMission::Task& task) const;

			// === 参数和配置管理 ===

			/**
			 * @brief 加载配置参数
			 */
			void loadParams();

			// === 评估和优化 ===

			/**
			 * @brief 评估任务执行成本
			 * @param task 任务对象
			 * @param uav 执行无人机
			 * @param route 规划的航线
			 * @param result 规划结果（用于添加评估信息）
			 * @return 轨迹成本
			 */
			TrajectoryCost evaluateTaskCost(const NSMission::Task& task,
				const NSUav::UavPtr& uav,
				const PlannedRoute& route,
				PlanningResult& result);

			/**
			 * @brief 计算无人机执行特定任务的适合度分数
			 * @param uav 无人机指针
			 * @param task 任务对象
			 * @param uav_state 无人机当前状态
			 * @return 适合度分数，越高越适合
			 */
			double calculateUavTaskFitness(const NSUav::UavPtr& uav,
										   const NSMission::Task& task,
										   const NSUav::UavState& uav_state) const;

			// === 任务信息提取 ===

			/**
			 * @brief 从任务中提取位置信息
			 * @param task 任务对象
			 * @return 任务位置，无法确定则返回空
			 */
			std::optional<NSCore::WGS84Point> getTaskPosition(const NSMission::Task& task) const;

			/**
			 * @brief 获取任务的载荷要求
			 * @param task 任务对象
			 * @return 载荷要求值，没有要求则返回空
			 */
			std::optional<double> getTaskPayloadRequirement(const NSMission::Task& task) const;

			// === 环境和算法组件访问 ===

			/**
			 * @brief 获取环境实例
			 * @return 环境实例的共享指针
			 */
			std::shared_ptr<NSEnvironment::Environment> getEnvironment() const;

			/**
			 * @brief 从环境获取路径规划器
			 * @return 路径规划器的共享指针，如果未配置则返回nullptr
			 */
			IPathPlannerPtr getPathPlanner() const;

			/**
			 * @brief 从环境获取轨迹优化器
			 * @return 轨迹优化器的共享指针，如果未配置则返回nullptr
			 */
			ITrajectoryOptimizerPtr getTrajectoryOptimizer() const;

			/**
			 * @brief 从环境获取轨迹评估器
			 * @return 轨迹评估器的共享指针，如果未配置则返回nullptr
			 */
			ITrajectoryEvaluatorPtr getTrajectoryEvaluator() const;

			/**
			 * @brief 从环境获取任务分配器
			 * @return 任务分配器的共享指针，如果未配置则返回nullptr
			 */
			ITaskAllocatorPtr getTaskAllocator() const;

			// === 成员变量 ===

			// 内部状态
			std::map<NSMission::TaskType, TaskPlannerPtr> task_planners_;  ///< 任务类型到规划器的映射
			std::vector<NSUav::UavPtr> available_uavs_;                    ///< 可用无人机列表
			mutable std::mutex uav_list_mutex_;                            ///< 保护无人机列表的互斥锁
		};

	} // namespace NSPlanning
} // namespace NSDrones