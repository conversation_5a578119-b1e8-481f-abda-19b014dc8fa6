// include/planning/task_planners/task_planner_loiterpoint.h
#pragma once

#include "planning/itask_planner.h"
#include "environment/environment_fwd.h"
#include "mission/task.h"
#include "mission/task_params.h"
#include "mission/control_point.h"

namespace NSDrones {
	namespace NSPlanning {
		using namespace NSDrones::NSUtils;
		using namespace NSDrones::NSCore;

		/**
		 * @class LoiterPointTaskPlanner
		 * @brief 定点盘旋任务规划器
		 *
		 * 负责规划LOITER_POINT类型任务，支持：
		 * - 圆形盘旋路径生成
		 * - 可配置的盘旋半径和持续时间
		 * - 顺时针/逆时针盘旋方向
		 * - 多圈盘旋支持
		 *
		 * ## 主要功能
		 * - **路径优化**: 计算最优的盘旋入口点
		 * - **时间控制**: 根据持续时间计算盘旋圈数
		 * - **精确定位**: 使用GeographicLib进行精确的地理计算
		 * - **动态适应**: 根据无人机性能调整盘旋参数
		 */
		class LoiterPointTaskPlanner : public ITaskPlanner {
		public:
			// === 生命周期管理 ===

			/**
			 * @brief 构造函数
			 */
			LoiterPointTaskPlanner();

			/**
			 * @brief 初始化盘旋任务规划器
			 * @param params 配置参数对象
			 * @param raw_config 原始JSON配置
			 * @return 初始化成功返回true
			 */
			bool initialize(std::shared_ptr<NSParams::ParamValues> params,
						   const nlohmann::json& raw_config) override;

			// === 核心接口实现 ===

			/**
			 * @brief 检查是否支持指定任务类型
			 * @param task_type 任务类型
			 * @return 支持LOITER_POINT类型返回true
			 */
			bool isTaskTypeSupported(NSMission::TaskType task_type) const override {
				return task_type == NSMission::TaskType::LOITER_POINT;
			}

			/**
			 * @brief 规划定点盘旋任务
			 * @param task 盘旋任务对象
			 * @param assigned_uavs 分配的无人机列表
			 * @param start_states 无人机起始状态映射
			 * @return 包含盘旋航线的规划结果
			 */
			PlanningResult planTask(const NSMission::Task& task,
								   const std::vector<NSUav::UavPtr>& assigned_uavs,
								   const std::map<NSUtils::ObjectID, NSUav::UavState>& start_states) override;

		private:
			// === 配置参数 ===
			int default_points_per_circle_ = 36;  ///< 默认每圈航点数
			double min_loiter_radius_ = 10.0;     ///< 最小盘旋半径(米)

			// === 私有辅助方法 ===

			/**
			 * @brief 验证盘旋任务参数
			 * @param params 盘旋任务参数
			 * @param task_id 任务ID（用于错误报告）
			 * @param result 结果对象（用于记录错误）
			 * @return 验证通过返回true
			 */
			bool validateLoiterParams(const NSMission::LoiterPointTaskParams& params,
									 const NSUtils::ObjectID& task_id,
									 PlanningResult& result) const;

			/**
			 * @brief 为单个无人机规划盘旋路径
			 * @param uav 目标无人机
			 * @param loiter_center 盘旋中心点
			 * @param radius 盘旋半径
			 * @param duration 盘旋持续时间
			 * @param clockwise 盘旋方向
			 * @param start_state 起始状态
			 * @param task 任务对象
			 * @param result 结果对象
			 * @return 规划成功返回true
			 */
			bool planLoiterPathForUav(const NSUav::UavPtr& uav,
									 const NSCore::WGS84Point& loiter_center,
									 double radius,
									 double duration,
									 bool clockwise,
									 const NSUav::UavState& start_state,
									 const NSMission::Task& task,
									 PlanningResult& result) const;

			/**
			 * @brief 使用GeometryManager生成盘旋路径点
			 * @param center 盘旋中心
			 * @param radius 盘旋半径
			 * @param num_points 路径点数量
			 * @param clockwise 盘旋方向
			 * @return 盘旋路径点列表
			 */
			std::vector<NSCore::WGS84Point> generateLoiterWaypoints(
				const NSCore::WGS84Point& center,
				double radius,
				int num_points,
				bool clockwise) const;
		};

	} // namespace NSPlanning
} // namespace NSDrones