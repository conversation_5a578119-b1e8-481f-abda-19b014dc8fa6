// src/mission/control_point.cpp
#include "mission/control_point.h"
#include "utils/logging.h"
#include <sstream>
#include <iomanip>

namespace NSDrones {
	namespace NSMission {
		using namespace NSDrones::NSUtils;
		using namespace NSDrones::NSCore;

		// --- PayloadActionCommand 实现 ---

		std::string PayloadActionCommand::getDescription() const {
			std::stringstream ss;
			ss << command_name;

			if (!payload_id.empty()) {
				ss << " (载荷: " << payload_id << ")";
			}

			if (!parameters.empty()) {
				ss << " [参数: " << parameters.size() << "个]";
			}

			if (delay_before_seconds > ControlPointConstants::FLOAT_COMPARISON_EPSILON) {
				ss << " [延迟: " << std::fixed << std::setprecision(1)
				   << delay_before_seconds << "s]";
			}

			if (is_critical) {
				ss << " [关键]";
			}

			return ss.str();
		}

		bool PayloadActionCommand::isValid() const {
			// 检查命令名称
			if (command_name.empty()) {
				LOG_TRACE("载荷动作命令无效：命令名称为空");
				return false;
			}

			// 检查时间参数
			if (delay_before_seconds < 0.0) {
				LOG_TRACE("载荷动作命令无效：延迟时间为负数 {}", delay_before_seconds);
				return false;
			}

			if (timeout_seconds <= ControlPointConstants::FLOAT_COMPARISON_EPSILON) {
				LOG_TRACE("载荷动作命令无效：超时时间无效 {}", timeout_seconds);
				return false;
			}

			// 检查参数有效性
			for (const auto& [key, value] : parameters) {
				if (key.empty()) {
					LOG_TRACE("载荷动作命令无效：参数键为空");
					return false;
				}
				// 这里可以添加更多参数值的验证
			}

			LOG_TRACE("载荷动作命令验证通过: {}", getDescription());
			return true;
		}

		// --- ControlPoint 实现 ---

		bool ControlPoint::isValid() const {
			// 检查位置有效性
			if (!position.isValid()) {
				LOG_TRACE("控制点无效：位置坐标无效 {}", position.toString());
				return false;
			}

			// 检查高度合理性
			if (!std::isfinite(required_altitude)) {
				LOG_TRACE("控制点无效：高度值无效 {}", required_altitude);
				return false;
			}

			// 检查高度范围
			if (required_altitude < ControlPointConstants::MIN_VALID_ALTITUDE ||
				required_altitude > ControlPointConstants::MAX_VALID_ALTITUDE) {
				LOG_TRACE("控制点无效：高度值超出有效范围 {} (范围: {}-{})",
						 required_altitude, ControlPointConstants::MIN_VALID_ALTITUDE,
						 ControlPointConstants::MAX_VALID_ALTITUDE);
				return false;
			}

			// 检查时间约束合理性
			if (required_loiter_time < 0.0) {
				LOG_TRACE("控制点无效：停留时间为负数 {}", required_loiter_time);
				return false;
			}

			if (arrival_time.has_value() && departure_time.has_value()) {
				if (departure_time.value() <= arrival_time.value()) {
					LOG_TRACE("控制点无效：离开时间 {} 早于或等于到达时间 {}",
							 departure_time.value(), arrival_time.value());
					return false;
				}
			}

			// 检查速度约束合理性
			if (min_speed.has_value()) {
				double min_spd = min_speed.value();
				if (min_spd < 0.0 || min_spd > ControlPointConstants::MAX_VALID_SPEED) {
					LOG_TRACE("控制点无效：最小速度超出有效范围 {} (范围: 0-{})",
							 min_spd, ControlPointConstants::MAX_VALID_SPEED);
					return false;
				}
			}

			if (max_speed.has_value()) {
				double max_spd = max_speed.value();
				if (max_spd < ControlPointConstants::MIN_VALID_SPEED ||
					max_spd > ControlPointConstants::MAX_VALID_SPEED) {
					LOG_TRACE("控制点无效：最大速度超出有效范围 {} (范围: {}-{})",
							 max_spd, ControlPointConstants::MIN_VALID_SPEED,
							 ControlPointConstants::MAX_VALID_SPEED);
					return false;
				}
			}

			if (min_speed.has_value() && max_speed.has_value()) {
				if (min_speed.value() > max_speed.value()) {
					LOG_TRACE("控制点无效：最小速度 {} 大于最大速度 {}",
							 min_speed.value(), max_speed.value());
					return false;
				}
			}

			// 检查航向角度范围
			if (required_heading_deg.has_value()) {
				double heading = required_heading_deg.value();
				if (heading < 0.0 || heading >= 360.0) {
					LOG_TRACE("控制点无效：航向角度超出范围 {} (范围: 0-360)", heading);
					return false;
				}
			}

			if (heading_tolerance_deg.has_value()) {
				double tolerance = heading_tolerance_deg.value();
				if (tolerance < 0.0 || tolerance > ControlPointConstants::ANGLE_WRAP_THRESHOLD) {
					LOG_TRACE("控制点无效：航向容差超出范围 {} (范围: 0-{})",
							 tolerance, ControlPointConstants::ANGLE_WRAP_THRESHOLD);
					return false;
				}
			}

			// 检查容差值
			if (position_tolerance.has_value() && position_tolerance.value() < 0.0) {
				LOG_TRACE("控制点无效：位置容差为负数 {}", position_tolerance.value());
				return false;
			}

			if (altitude_tolerance.has_value() && altitude_tolerance.value() < 0.0) {
				LOG_TRACE("控制点无效：高度容差为负数 {}", altitude_tolerance.value());
				return false;
			}

			// 检查载荷动作有效性
			if (action && !action->isValid()) {
				LOG_TRACE("控制点无效：载荷动作无效");
				return false;
			}

			for (const auto& cmd : action_commands) {
				if (!cmd.isValid()) {
					LOG_TRACE("控制点无效：载荷动作命令无效 {}", cmd.getDescription());
					return false;
				}
			}

			LOG_TRACE("控制点验证通过: {}", getDetailedDescription());
			return true;
		}

		std::string ControlPoint::generateDetailedDescriptionInternal() const {
			std::stringstream ss;

			// 基本信息
			if (!name.empty()) {
				ss << name;
			} else {
				ss << "控制点";
			}

			ss << " @ " << position.toString();

			// 类型信息
			switch (type) {
				case ControlPointType::START:
					ss << " [起始点]";
					break;
				case ControlPointType::END:
					ss << " [结束点]";
					break;
				case ControlPointType::WAYPOINT_MUST_PASS:
					ss << " [必经点]";
					break;
				case ControlPointType::WAYPOINT_CONSTRAINT:
					ss << " [约束点]";
					break;
				default:
					ss << " [未知类型]";
					break;
			}

			// 高度信息
			ss << " (高度: " << std::fixed << std::setprecision(1) << required_altitude << "m";
			switch (height_type) {
				case AltitudeType::ABSOLUTE_ALTITUDE:
					ss << " MSL";
					break;
				case AltitudeType::ABOVE_GROUND_LEVEL:
					ss << " AGL";
					break;
				default:
					break;
			}
			ss << ")";

			// 约束信息
			std::vector<std::string> constraints;

			if (hasAnySpeedConstraint()) {
				std::string speed_info = "速度";
				if (required_speed.has_value()) {
					const auto& spd = required_speed.value();
					speed_info += fmt::format("({:.1f},{:.1f},{:.1f})", spd.x(), spd.y(), spd.z());
				}
				if (min_speed.has_value() || max_speed.has_value()) {
					speed_info += "[";
					if (min_speed.has_value()) speed_info += fmt::format("{:.1f}", min_speed.value());
					speed_info += "-";
					if (max_speed.has_value()) speed_info += fmt::format("{:.1f}", max_speed.value());
					speed_info += "m/s]";
				}
				constraints.push_back(speed_info);
			}

			if (hasAnyAttitudeConstraint()) {
				std::string attitude_info = "姿态";
				if (required_heading_deg.has_value()) {
					attitude_info += fmt::format("(航向:{:.1f}°)", required_heading_deg.value());
				}
				constraints.push_back(attitude_info);
			}

			if (hasAnyTimeConstraint()) {
				std::string time_info = "时间";
				if (required_loiter_time > ControlPointConstants::FLOAT_COMPARISON_EPSILON) {
					time_info += fmt::format("(停留:{:.1f}s)", required_loiter_time);
				}
				if (arrival_time.has_value()) {
					time_info += fmt::format("(到达:{:.1f}s)", arrival_time.value());
				}
				constraints.push_back(time_info);
			}

			if (hasAnyPayloadAction()) {
				std::string payload_info = "载荷";
				int action_count = 0;
				if (action) action_count++;
				action_count += static_cast<int>(action_commands.size());
				payload_info += fmt::format("({}个动作)", action_count);
				constraints.push_back(payload_info);
			}

			if (hasAnyTolerance()) {
				std::string tolerance_info = "容差";
				if (position_tolerance.has_value()) {
					tolerance_info += fmt::format("(位置:{:.1f}m)", position_tolerance.value());
				}
				if (altitude_tolerance.has_value()) {
					tolerance_info += fmt::format("(高度:{:.1f}m)", altitude_tolerance.value());
				}
				constraints.push_back(tolerance_info);
			}

			if (!constraints.empty()) {
				ss << " [";
				for (size_t i = 0; i < constraints.size(); ++i) {
					if (i > 0) ss << ", ";
					ss << constraints[i];
				}
				ss << "]";
			}

			// 强制性标志
			if (!is_mandatory) {
				ss << " [可选]";
			}

			// 描述信息
			if (!description.empty()) {
				ss << " - " << description;
			}

			return ss.str();
		}

		double ControlPoint::distanceTo(const ControlPoint& other) const {
			try {
				return NSUtils::GeometryManager::calculate3DDistance(position, other.position);
			}
			catch (const std::exception& e) {
				LOG_WARN("控制点距离计算失败: {}", e.what());
				return 0.0;
			}
		}

		double ControlPoint::getEstimatedDwellTime() const {
			// 使用缓存优化性能
			if (cache_dirty_ || !cached_dwell_time_.has_value()) {
				updateCache();
			}
			return cached_dwell_time_.value();
		}

		std::string ControlPoint::getDetailedDescription() const {
			// 使用缓存优化性能
			if (cache_dirty_ || !cached_description_.has_value()) {
				updateCache();
			}
			return cached_description_.value();
		}

		// --- 私有缓存方法实现 ---

		void ControlPoint::updateCache() const {
			if (!cache_dirty_) return;

			// 更新停留时间缓存
			cached_dwell_time_ = calculateDwellTimeInternal();

			// 更新描述缓存
			cached_description_ = generateDetailedDescriptionInternal();

			// 标记缓存为最新
			cache_dirty_ = false;

			LOG_TRACE("控制点缓存已更新: {}", name.empty() ? "未命名点" : name);
		}

		double ControlPoint::calculateDwellTimeInternal() const {
			double total_time = required_loiter_time;

			// 添加载荷动作时间
			if (action) {
				total_time += action->getEstimatedDuration();
			}

			for (const auto& cmd : action_commands) {
				total_time += cmd.delay_before_seconds + cmd.timeout_seconds;
			}

			return total_time;
		}

		bool ControlPoint::isArrivalConditionMet(const WGS84Point& current_pos,
												double current_heading) const {
			try {
				// 验证输入参数
				if (!current_pos.isValid()) {
					LOG_WARN("控制点到达条件检查失败：当前位置无效 {}", current_pos.toString());
					return false;
				}

				// 检查位置容差
				double distance = NSUtils::GeometryManager::calculate3DDistance(position, current_pos);
				double pos_tolerance = getPositionToleranceOrDefault();
				if (distance > pos_tolerance) {
					LOG_TRACE("控制点到达条件未满足：位置距离 {:.2f}m 超过容差 {:.2f}m",
							 distance, pos_tolerance);
					return false;
				}

				// 检查高度容差
				double alt_diff = std::abs(current_pos.altitude - required_altitude);
				double alt_tolerance = getAltitudeToleranceOrDefault();
				if (alt_diff > alt_tolerance) {
					LOG_TRACE("控制点到达条件未满足：高度差 {:.2f}m 超过容差 {:.2f}m",
							 alt_diff, alt_tolerance);
					return false;
				}

				// 检查航向容差
				if (hasHeadingRequirement()) {
					// 规范化航向角度到 [0, 360) 范围
					double normalized_current = std::fmod(current_heading + 360.0, 360.0);
					double normalized_required = std::fmod(required_heading_deg.value() + 360.0, 360.0);

					double heading_diff = std::abs(normalized_current - normalized_required);
					// 处理角度环绕（选择较小的角度差）
					if (heading_diff > ControlPointConstants::ANGLE_WRAP_THRESHOLD) {
						heading_diff = 360.0 - heading_diff;
					}

					double heading_tolerance = getHeadingToleranceOrDefault();
					if (heading_diff > heading_tolerance) {
						LOG_TRACE("控制点到达条件未满足：航向差 {:.1f}° 超过容差 {:.1f}° (当前:{:.1f}°, 要求:{:.1f}°)",
								 heading_diff, heading_tolerance, normalized_current, normalized_required);
						return false;
					}
				}

				LOG_TRACE("控制点到达条件满足: {} (距离:{:.2f}m, 高度差:{:.2f}m)",
						 name.empty() ? "未命名点" : name, distance, alt_diff);
				return true;
			}
			catch (const std::exception& e) {
				LOG_WARN("控制点到达条件检查失败: {}", e.what());
				return false;
			}
		}

	} // namespace NSMission
} // namespace NSDrones
