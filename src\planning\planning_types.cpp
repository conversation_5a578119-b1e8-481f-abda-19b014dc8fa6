// src/planning/planning_types.cpp
#include "planning/planning_types.h"
#include "utils/geometry_manager.h"
#include "utils/coordinate_converter.h"
#include "uav/idynamic_model.h"
#include "uav/uav_types.h"
#include "utils/logging.h"
#include "utils/object_id.h"
#include <cmath>
#include <stdexcept>
#include <numeric>
#include <algorithm>

namespace NSDrones {
	namespace NSPlanning {

		// === PlannedRoute类实现 ===

		PlannedRoute::PlannedRoute(NSUtils::ObjectID uav_id) : uav_id_(std::move(uav_id)) {
			if (!NSUtils::isValidObjectID(uav_id_)) {
				LOG_WARN("规划航线: 创建时使用了无效的无人机ID '{}'", uav_id_);
			}
			LOG_DEBUG("规划航线: 已创建，无人机ID: '{}'", uav_id_);
		}

		const NSUtils::ObjectID& PlannedRoute::getUavId() const {
			return uav_id_;
		}

		const RouteSegment& PlannedRoute::getWaypoints() const {
			return waypoints_;
		}

		RouteSegment& PlannedRoute::getWaypoints() {
			return waypoints_;
		}

		void PlannedRoute::addWaypoint(const RoutePoint& point) {
			if (!waypoints_.empty()) {
				// 检查时间戳单调性
				if (point.time_stamp < waypoints_.back().time_stamp - NSCore::Constants::TIME_EPSILON) {
					LOG_WARN("规划航线[{}]: 航点时间戳({:.4f})早于前一个({:.4f})",
						uav_id_, point.time_stamp, waypoints_.back().time_stamp);
				}

				// 使用GeometryManager计算距离
				double distance = NSUtils::GeometryManager::calculateDistance(
					waypoints_.back().position, point.position);

				// 检查是否为冗余点
				if (distance < NSCore::Constants::GEOMETRY_EPSILON &&
					std::abs(point.time_stamp - waypoints_.back().time_stamp) < NSCore::Constants::TIME_EPSILON) {

					// 合并载荷动作
					if (!point.payload_actions.empty()) {
						waypoints_.back().payload_actions.insert(waypoints_.back().payload_actions.end(),
							point.payload_actions.begin(), point.payload_actions.end());
					}
					return; // 跳过冗余点
				}
			}
			waypoints_.push_back(point);
		}

		void PlannedRoute::addWaypoints(const RouteSegment& segment) {
			if (segment.empty()) {
				return;
			}

			if (waypoints_.empty()) {
				waypoints_ = segment;
				return;
			}

			// 检查时间戳连续性
			if (segment.front().time_stamp < waypoints_.back().time_stamp - NSCore::Constants::TIME_EPSILON) {
				LOG_WARN("规划航线[{}]: 航段起始时间({:.4f})早于路径结束时间({:.4f})",
					uav_id_, segment.front().time_stamp, waypoints_.back().time_stamp);
			}

			// 使用GeometryManager计算距离
			double distance = NSUtils::GeometryManager::calculateDistance(
				waypoints_.back().position, segment.front().position);

			// 检查连接点是否重复
			if (distance < NSCore::Constants::GEOMETRY_EPSILON &&
				std::abs(waypoints_.back().time_stamp - segment.front().time_stamp) < NSCore::Constants::TIME_EPSILON) {

				// 合并载荷动作
				if (!segment.front().payload_actions.empty()) {
					waypoints_.back().payload_actions.insert(waypoints_.back().payload_actions.end(),
						segment.front().payload_actions.begin(), segment.front().payload_actions.end());
				}
				// 插入从第二个点开始的段
				waypoints_.insert(waypoints_.end(), std::next(segment.begin()), segment.end());
			}
			else {
				// 直接插入整个段
				waypoints_.insert(waypoints_.end(), segment.begin(), segment.end());
			}
		}

		void PlannedRoute::clearWaypoints() {
			waypoints_.clear();
		}

		bool PlannedRoute::isEmpty() const {
			return waypoints_.empty();
		}

		double PlannedRoute::getTotalLength() const {
			if (waypoints_.size() < 2) {
				return 0.0;
			}

			double total_length = 0.0;
			for (size_t i = 0; i < waypoints_.size() - 1; ++i) {
				total_length += NSUtils::GeometryManager::calculateDistance(
					waypoints_[i].position, waypoints_[i + 1].position);
			}
			return total_length;
		}

		NSCore::Time PlannedRoute::getTotalTime() const {
			if (waypoints_.size() < 2) {
				return 0.0;
			}

			// 检查时间戳有效性
			if (waypoints_.back().time_stamp < waypoints_.front().time_stamp - NSCore::Constants::TIME_EPSILON) {
				LOG_ERROR("规划航线[{}]: 结束时间({:.4f})早于起始时间({:.4f})",
					uav_id_, waypoints_.back().time_stamp, waypoints_.front().time_stamp);
				return 0.0;
			}

			return waypoints_.back().time_stamp - waypoints_.front().time_stamp;
		}

		const RoutePoint& PlannedRoute::getStartPoint() const {
			if (isEmpty()) {
				throw DroneException("尝试从无人机[" + uav_id_ + "]的空航线获取起始点", NSCore::ErrorCode::INVALID_STATE);
			}
			return waypoints_.front();
		}

		const RoutePoint& PlannedRoute::getEndPoint() const {
			if (isEmpty()) {
				throw DroneException("尝试从无人机[" + uav_id_ + "]的空航线获取结束点", NSCore::ErrorCode::INVALID_STATE);
			}
			return waypoints_.back();
		}


		// === 全局辅助函数实现 ===

		bool timeParameterizeConstantSpeed(
			const std::vector<NSCore::WGS84Point>& path_geometry,
			const NSUav::IDynamicModel* dynamics,
			NSCore::Time start_time,
			const NSCore::Vector3D& start_velocity,
			double desired_speed,
			RouteSegment& result)
		{
			LOG_DEBUG("时间参数化: 开始处理{}个点的路径，期望速度{:.2f}m/s",
				path_geometry.size(), desired_speed);

			result.clear();

			// 输入验证
			if (path_geometry.empty()) {
				LOG_WARN("时间参数化: 路径为空");
				return false;
			}

			if (path_geometry.size() == 1) {
				// 单点路径处理
				RoutePoint rp;
				rp.position = path_geometry[0];
				rp.time_stamp = start_time;
				rp.velocity = start_velocity;

				if (start_velocity.norm() > NSCore::Constants::VELOCITY_EPSILON) {
					rp.orientation = NSCore::Orientation::FromTwoVectors(
						NSCore::Vector3D::UnitX(), start_velocity.normalized());
					rp.orientation.normalize();
				}
				else {
					rp.orientation = NSCore::Orientation::Identity();
				}

				result.push_back(rp);
				return true;
			}

			// 速度验证和调整
			double actual_speed = desired_speed;
			if (actual_speed <= NSCore::Constants::VELOCITY_EPSILON) {
				LOG_ERROR("时间参数化: 期望速度无效({:.4f})", actual_speed);
				return false;
			}

			// 动力学约束检查
			if (dynamics) {
				NSUav::UavState dummy_state;
				dummy_state.position = path_geometry[0];

				double min_op_speed = dynamics->getMinOperationalSpeed(dummy_state);
				dummy_state.mode = (actual_speed < min_op_speed + 1.0) ?
					NSUav::FlightMode::HOVER : NSUav::FlightMode::FIXED_WING;

				double max_h_speed = dynamics->getMaxHorizontalSpeed(dummy_state);

				if (max_h_speed <= NSCore::Constants::VELOCITY_EPSILON) {
					LOG_WARN("时间参数化: 动力学模型返回无效最大速度({:.2f}), 使用默认15.0m/s", max_h_speed);
					max_h_speed = 15.0;
				}

				if (actual_speed > max_h_speed + NSCore::Constants::VELOCITY_EPSILON) {
					LOG_WARN("时间参数化: 期望速度({:.2f})超过最大速度({:.2f}), 已限制",
						actual_speed, max_h_speed);
					actual_speed = max_h_speed;

					if (actual_speed <= NSCore::Constants::VELOCITY_EPSILON) {
						LOG_ERROR("时间参数化: 限制后速度仍无效({:.4f})", actual_speed);
						return false;
					}
				}
			}


			result.reserve(path_geometry.size());

			// 创建起始点
			RoutePoint current_rp;
			current_rp.position = path_geometry[0];
			current_rp.time_stamp = start_time;
			current_rp.velocity = start_velocity;

			// 设置起始姿态
			NSCore::Vector3D initial_dir = NSCore::Vector3D::UnitX();
			if (start_velocity.norm() > NSCore::Constants::VELOCITY_EPSILON) {
				initial_dir = start_velocity.normalized();
			}
			else {
				// 使用GeometryManager计算第一段方向
				double distance = NSUtils::GeometryManager::calculateDistance(
					path_geometry[0], path_geometry[1]);
				if (distance > NSCore::Constants::GEOMETRY_EPSILON) {
					double bearing_deg = NSUtils::GeometryManager::calculateBearing(
						path_geometry[0], path_geometry[1]);
					// 将方位角转换为方向向量（NED坐标系）
					double bearing_rad = bearing_deg * NSCore::Constants::DEG_TO_RAD;
					initial_dir = NSCore::Vector3D(std::cos(bearing_rad), std::sin(bearing_rad), 0.0);
				}
			}

			// 安全的姿态计算
			NSCore::Vector3D ref_vec = NSCore::Vector3D::UnitX();
			if ((ref_vec - initial_dir).norm() < NSCore::Constants::ANGLE_EPSILON ||
				(ref_vec + initial_dir).norm() < NSCore::Constants::ANGLE_EPSILON) {
				ref_vec = NSCore::Vector3D::UnitY();
			}
			current_rp.orientation = NSCore::Orientation::FromTwoVectors(ref_vec, initial_dir);
			current_rp.orientation.normalize();

			result.push_back(current_rp);

			NSCore::Time current_time = start_time;

			// 处理路径段
			for (size_t i = 0; i < path_geometry.size() - 1; ++i) {
				const NSCore::WGS84Point& p1 = path_geometry[i];
				const NSCore::WGS84Point& p2 = path_geometry[i + 1];

				// 使用GeometryManager计算段长度
				double segment_len = NSUtils::GeometryManager::calculateDistance(p1, p2);

				// 跳过零长度段
				if (segment_len < NSCore::Constants::GEOMETRY_EPSILON) {
					continue;
				}

				// 计算段方向
				double bearing_deg = NSUtils::GeometryManager::calculateBearing(p1, p2);
				double bearing_rad = bearing_deg * NSCore::Constants::DEG_TO_RAD;
				NSCore::Vector3D segment_dir(std::cos(bearing_rad), std::sin(bearing_rad), 0.0);

				NSCore::Time segment_time = segment_len / actual_speed;
				current_time += segment_time;

				RoutePoint next_rp;
				next_rp.position = p2;
				next_rp.time_stamp = current_time;
				next_rp.velocity = segment_dir * actual_speed;

				// 计算姿态
				NSCore::Vector3D ref_vec = NSCore::Vector3D::UnitX();
				if ((ref_vec - segment_dir).norm() < NSCore::Constants::ANGLE_EPSILON ||
					(ref_vec + segment_dir).norm() < NSCore::Constants::ANGLE_EPSILON) {
					ref_vec = NSCore::Vector3D::UnitY();
				}
				next_rp.orientation = NSCore::Orientation::FromTwoVectors(ref_vec, segment_dir);
				next_rp.orientation.normalize();

				result.push_back(next_rp);
			}

			// 结果验证
			if (result.empty()) {
				LOG_ERROR("时间参数化: 未能生成任何航点");
				return false;
			}

			// 处理所有段都太短的情况
			if (result.size() == 1 && path_geometry.size() > 1) {
				LOG_WARN("时间参数化: 所有路径段长度接近零，添加终点");
				RoutePoint end_rp = result.back();
				end_rp.position = path_geometry.back();
				result.push_back(end_rp);
			}

			LOG_DEBUG("时间参数化: 完成，生成{}个航点", result.size());
			return true;
		}

	} // namespace NSPlanning
} // namespace NSDrones