// include/planning/task_planners/task_planner_surveymultipoints.h
#pragma once

#include "planning/itask_planner.h"
#include "environment/environment_fwd.h"
#include "mission/task.h"
#include "mission/task_params.h"

namespace NSDrones {
	namespace NSPlanning {
		using namespace NSDrones::NSUtils;
		using namespace NSDrones::NSCore;

		/**
		 * @class SurveyMultiPointsTaskPlanner
		 * @brief 多点巡检任务规划器
		 *
		 * 负责规划SURVEY_MULTIPOINTS类型任务，支持：
		 * - 多个巡检点的顺序访问
		 * - 路径优化和时间控制
		 * - 可配置的巡检参数
		 * - 多无人机协同巡检
		 */
		class SurveyMultiPointsTaskPlanner : public ITaskPlanner {
		public:
			// === 生命周期管理 ===

			/**
			 * @brief 构造函数
			 */
			SurveyMultiPointsTaskPlanner();

			/**
			 * @brief 初始化多点巡检任务规划器
			 * @param params 配置参数对象
			 * @param raw_config 原始JSON配置
			 * @return 初始化成功返回true
			 */
			bool initialize(std::shared_ptr<NSParams::ParamValues> params,
						   const nlohmann::json& raw_config) override;

			// === 核心接口实现 ===

			/**
			 * @brief 检查是否支持指定任务类型
			 * @param task_type 任务类型
			 * @return 支持SURVEY_MULTIPOINTS类型返回true
			 */
			bool isTaskTypeSupported(NSMission::TaskType task_type) const override {
				return task_type == NSMission::TaskType::SURVEY_MULTIPOINTS;
			}

			/**
			 * @brief 规划多点巡检任务
			 * @param task 巡检任务对象
			 * @param assigned_uavs 分配的无人机列表
			 * @param start_states 无人机起始状态映射
			 * @return 包含巡检航线的规划结果
			 */
			PlanningResult planTask(const NSMission::Task& task,
								   const std::vector<NSUav::UavPtr>& assigned_uavs,
								   const std::map<NSUtils::ObjectID, NSUav::UavState>& start_states) override;

		private:
			// === 配置参数 ===
			double default_survey_speed_ = 8.0;  ///< 默认巡检速度(米/秒)

			// === 私有辅助方法 ===

			/**
			 * @brief 验证多点巡检任务参数
			 * @param params 巡检任务参数
			 * @param task_id 任务ID（用于错误报告）
			 * @param result 结果对象（用于记录错误）
			 * @return 验证通过返回true
			 */
			bool validateSurveyParams(const NSMission::SurveyMultiPointsTaskParams& params,
									 const NSUtils::ObjectID& task_id,
									 PlanningResult& result) const;

			/**
			 * @brief 为单个无人机规划巡检路径
			 * @param uav 目标无人机
			 * @param survey_points 巡检点列表
			 * @param start_state 起始状态
			 * @param task 任务对象
			 * @param result 结果对象
			 * @return 规划成功返回true
			 */
			bool planSurveyPathForUav(const NSUav::UavPtr& uav,
									 const std::vector<NSCore::WGS84Point>& survey_points,
									 const NSUav::UavState& start_state,
									 const NSMission::Task& task,
									 PlanningResult& result) const;
		};

	} // namespace NSPlanning
} // namespace NSDrones