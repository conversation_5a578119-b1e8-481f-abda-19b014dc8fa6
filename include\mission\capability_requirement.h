// include/mission/capability_requirement.h
#pragma once

#include "core/types.h" 
#include <vector>
#include <string>
#include <optional>
#include <algorithm> 

namespace NSDrones {
	namespace NSMission {

		using namespace NSDrones::NSUtils;
		using namespace NSDrones::NSCore;
		/**
		 * @class CapabilityRequirement
		 * @brief 任务对无人机能力的需求规格
		 *
		 * 定义了执行特定任务所需的无人机最低能力要求，包括无人机类型、
		 * 数量、载荷配置、性能指标等方面的约束条件。用于任务分配时
		 * 筛选符合条件的无人机。
		 */
		class CapabilityRequirement {
		public:
			// --- 基本要求 ---
			std::optional<UavType> required_uav_type;  ///< 要求的无人机类型（可选）
			size_t min_required_count;                 ///< 最小需要的无人机数量
			size_t max_required_count;                 ///< 最大允许的无人机数量

			// --- 载荷要求 ---
			std::vector<std::string> required_payloads;  ///< 必须携带的载荷名称列表
			std::vector<std::string> preferred_payloads; ///< 倾向携带的载荷名称列表

			// --- 性能要求 ---
			std::optional<Time> min_endurance_time;     ///< 最小续航时间要求（秒）
			std::optional<double> min_range;            ///< 最小航程要求（米）
			std::optional<double> min_payload_capacity; ///< 最小载荷能力（千克）
			std::optional<double> max_wind_resistance;  ///< 最大抗风能力（米/秒）
			std::optional<double> min_service_ceiling;  ///< 最小实用升限（米）

			// --- 构造函数 ---
			/**
			 * @brief 默认构造函数，创建基本的能力需求
			 */
			CapabilityRequirement()
				: min_required_count(1), max_required_count(1) {}

			/** @brief 设置所需无人机类型 */
			CapabilityRequirement& setType(UavType type) { required_uav_type = type; return *this; }
			/** @brief 设置所需数量 (最小和最大) */
			CapabilityRequirement& setCount(size_t min_count, size_t max_count = 0) {
				min_required_count = std::max(size_t(1), min_count); // 至少需要 1 架
				max_required_count = (max_count == 0) ? min_required_count : std::max(min_required_count, max_count); // 如果 max 为 0，则等于 min
				return *this;
			}
			/** @brief 添加必需载荷 */
			CapabilityRequirement& addRequiredPayload(const std::string& payload_name) {
				if (!payload_name.empty()) required_payloads.push_back(payload_name);
				return *this;
			}
			/** @brief 添加倾向载荷 */
			CapabilityRequirement& addPreferredPayload(const std::string& payload_name) {
				if (!payload_name.empty()) preferred_payloads.push_back(payload_name);
				return *this;
			}
			/** @brief 设置最小续航时间 */
			CapabilityRequirement& setMinEndurance(Time duration) { min_endurance_time = std::max(0.0, duration); return *this; }
			/** @brief 设置最小航程 */
			CapabilityRequirement& setMinRange(double range) { min_range = std::max(0.0, range); return *this; }
			/** @brief 设置最小载荷能力 */
			CapabilityRequirement& setMinPayloadCapacity(double capacity) { min_payload_capacity = std::max(0.0, capacity); return *this; }


			/**
			 * @brief 检查需求是否为空（未设置任何特殊要求）
			 * @return true表示只有基本的数量要求，没有其他特殊需求
			 */
			bool isEmpty() const {
				return !required_uav_type.has_value() &&
					   required_payloads.empty() &&
					   preferred_payloads.empty() &&
					   !min_endurance_time.has_value() &&
					   !min_range.has_value() &&
					   !min_payload_capacity.has_value() &&
					   !max_wind_resistance.has_value() &&
					   !min_service_ceiling.has_value() &&
					   min_required_count == 1 &&
					   max_required_count == 1;
			}

			/**
			 * @brief 验证需求参数的有效性
			 * @return true表示所有设置的参数都在合理范围内
			 */
			bool isValid() const {
				// 检查数量要求
				if (min_required_count == 0 || max_required_count < min_required_count) {
					return false;
				}

				// 检查性能参数
				if (min_endurance_time.has_value() && min_endurance_time.value() < 0.0) {
					return false;
				}
				if (min_range.has_value() && min_range.value() < 0.0) {
					return false;
				}
				if (min_payload_capacity.has_value() && min_payload_capacity.value() < 0.0) {
					return false;
				}
				if (max_wind_resistance.has_value() && max_wind_resistance.value() < 0.0) {
					return false;
				}
				if (min_service_ceiling.has_value() && min_service_ceiling.value() < 0.0) {
					return false;
				}

				return true;
			}

			/**
			 * @brief 获取需求的描述信息
			 * @return 包含所有设置需求的描述字符串
			 */
			std::string getDescription() const {
				if (isEmpty()) {
					return "基本能力需求（1架无人机）";
				}

				std::string desc = "能力需求: ";
				std::vector<std::string> requirements;

				if (required_uav_type.has_value()) {
					requirements.push_back("类型 " + NSUtils::enumToString(required_uav_type.value()));
				}
				if (min_required_count != 1 || max_required_count != 1) {
					requirements.push_back("数量 " + std::to_string(min_required_count) +
										  "-" + std::to_string(max_required_count) + "架");
				}
				if (!required_payloads.empty()) {
					requirements.push_back("必需载荷 " + std::to_string(required_payloads.size()) + "种");
				}
				if (min_endurance_time.has_value()) {
					requirements.push_back("最小续航 " + std::to_string(min_endurance_time.value()) + "秒");
				}

				for (size_t i = 0; i < requirements.size(); ++i) {
					if (i > 0) desc += ", ";
					desc += requirements[i];
				}

				return desc;
			}

		private:
			// 私有辅助方法可以在这里添加
		};

	} // namespace NSMission
} // namespace NSDrones