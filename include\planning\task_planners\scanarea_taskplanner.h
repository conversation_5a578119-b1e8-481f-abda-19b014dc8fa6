// include/planning/task_planners/task_planner_scanarea.h
#pragma once

#include "planning/itask_planner.h"
#include "mission/task.h"
#include "mission/task_params.h"

namespace NSDrones {
	namespace NSPlanning {
		using namespace NSDrones::NSUtils;
		using namespace NSDrones::NSCore;

		/**
		 * @class ScanAreaTaskPlanner
		 * @brief 区域扫描任务规划器
		 *
		 * 负责规划SCAN_AREA类型任务，支持：
		 * - 任意形状区域的扫描路径生成
		 * - 多种扫描模式（之字形、螺旋形等）
		 * - 倾斜平面扫描支持
		 * - 可配置的重叠率和扫描参数
		 *
		 * ## 主要功能
		 * - **边界验证**: 检查扫描区域边界点的共面性
		 * - **路径生成**: 使用GeometryManager生成优化的扫描路径
		 * - **多机协调**: 支持多无人机协同扫描
		 * - **参数优化**: 根据相机参数和飞行性能优化扫描参数
		 */
		class ScanAreaTaskPlanner : public ITaskPlanner {
		public:
			// === 生命周期管理 ===

			/**
			 * @brief 构造函数
			 */
			ScanAreaTaskPlanner();

			/**
			 * @brief 初始化扫描任务规划器
			 * @param params 配置参数对象
			 * @param raw_config 原始JSON配置
			 * @return 初始化成功返回true
			 */
			bool initialize(std::shared_ptr<NSParams::ParamValues> params,
						   const nlohmann::json& raw_config) override;

			// === 核心接口实现 ===

			/**
			 * @brief 检查是否支持指定任务类型
			 * @param task_type 任务类型
			 * @return 支持SCAN_AREA类型返回true
			 */
			bool isTaskTypeSupported(NSMission::TaskType task_type) const override {
				return task_type == NSMission::TaskType::SCAN_AREA;
			}

			/**
			 * @brief 规划区域扫描任务
			 * @param task 扫描任务对象
			 * @param assigned_uavs 分配的无人机列表
			 * @param start_states 无人机起始状态映射
			 * @return 包含扫描航线的规划结果
			 */
			PlanningResult planTask(const NSMission::Task& task,
								   const std::vector<NSUav::UavPtr>& assigned_uavs,
								   const std::map<NSUtils::ObjectID, NSUav::UavState>& start_states) override;

		private:
			// === 配置参数 ===
			double default_scan_altitude_ = 50.0;    ///< 默认扫描高度(米)
			double default_scan_speed_ = 10.0;       ///< 默认扫描速度(米/秒)
			double default_overlap_ratio_ = 0.3;     ///< 默认重叠率(0.0-1.0)
			NSPlanning::ScanPatternType default_scan_pattern_ = NSPlanning::ScanPatternType::ZIGZAG; ///< 默认扫描模式
			double camera_fov_deg_ = 60.0;           ///< 相机视场角(度)

			// === 私有辅助方法 ===

			/**
			 * @brief 验证扫描区域参数
			 * @param params 扫描任务参数
			 * @param task_id 任务ID（用于错误报告）
			 * @param result 结果对象（用于记录错误）
			 * @return 验证通过返回true
			 */
			bool validateScanAreaParams(const NSMission::ScanAreaTaskParams& params,
									   const NSUtils::ObjectID& task_id,
									   PlanningResult& result) const;

			/**
			 * @brief 为单个无人机规划扫描路径
			 * @param uav 目标无人机
			 * @param scan_path_ecef 扫描路径点(ECEF坐标)
			 * @param start_state 起始状态
			 * @param task 任务对象
			 * @param result 结果对象
			 * @return 规划成功返回true
			 */
			bool planScanPathForUav(const NSUav::UavPtr& uav,
								   const std::vector<NSCore::EcefPoint>& scan_path_ecef,
								   const NSUav::UavState& start_state,
								   const NSMission::Task& task,
								   PlanningResult& result) const;
		};

	} // namespace NSPlanning
} // namespace NSDrones