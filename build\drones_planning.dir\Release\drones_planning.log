﻿  followpath_taskplanner.cpp
  loiterpoint_taskplanner.cpp
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(206,9): error C2662: “bool NSDrones::NSPlanning::ITaskPlanner::smoothAndTimeParameterize(const std::vector<NSDrones::NSCore::WGS84Point,std::allocator<NSDrones::NSCore::WGS84Point>> &,const NSDrones::NSUav::UavPtr &,const NSDrones::NSUav::UavState &,double,NSDrones::NSPlanning::RouteSegment &,NSDrones::NSPlanning::PlanningResult *,const NSDrones::NSMission::ITaskStrategyMap &)”: 不能将“this”指针从“const NSDrones::NSPlanning::LoiterPointTaskPlanner”转换为“NSDrones::NSPlanning::ITaskPlanner &”
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(206,9): message : 转换丢失限定符
E:\source\dronesplanning\include\planning/itask_planner.h(231,9): message : 参见“NSDrones::NSPlanning::ITaskPlanner::smoothAndTimeParameterize”的声明
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(206,9): message : 尝试匹配参数列表“(std::vector<NSDrones::NSCore::WGS84Point,std::allocator<NSDrones::NSCore::WGS84Point>>, const NSDrones::NSUav::UavPtr, const NSDrones::NSUav::UavState, double, NSDrones::NSPlanning::RouteSegment, NSDrones::NSPlanning::PlanningResult *, const NSDrones::NSMission::ITaskStrategyMap)”时
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(231,28): error C2039: "generateCircularPath": 不是 "NSDrones::NSUtils::GeometryManager" 的成员
E:\source\dronesplanning\include\environment/environment.h(41,28): message : 参见“NSDrones::NSUtils::GeometryManager”的声明
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(231,48): error C3861: “generateCircularPath”: 找不到标识符
  scanarea_taskplanner.cpp
E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp(223,9): error C2662: “bool NSDrones::NSPlanning::ITaskPlanner::smoothAndTimeParameterizeECEF(const std::vector<NSDrones::NSCore::EcefPoint,std::allocator<NSDrones::NSCore::EcefPoint>> &,const NSDrones::NSUav::UavPtr &,const NSDrones::NSUav::UavState &,double,NSDrones::NSPlanning::RouteSegment &,NSDrones::NSPlanning::PlanningResult *,const NSDrones::NSMission::ITaskStrategyMap &)”: 不能将“this”指针从“const NSDrones::NSPlanning::ScanAreaTaskPlanner”转换为“NSDrones::NSPlanning::ITaskPlanner &”
E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp(223,9): message : 转换丢失限定符
E:\source\dronesplanning\include\planning/itask_planner.h(254,9): message : 参见“NSDrones::NSPlanning::ITaskPlanner::smoothAndTimeParameterizeECEF”的声明
E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp(223,9): message : 尝试匹配参数列表“(std::vector<NSDrones::NSCore::EcefPoint,std::allocator<NSDrones::NSCore::EcefPoint>>, const NSDrones::NSUav::UavPtr, const NSDrones::NSUav::UavState, double, NSDrones::NSPlanning::RouteSegment, NSDrones::NSPlanning::PlanningResult *, const NSDrones::NSMission::ITaskStrategyMap)”时
  surveymultipoints_taskplanner.cpp
E:\source\dronesplanning\src\planning\task_planners\surveymultipoints_taskplanner.cpp(169,10): error C2662: “bool NSDrones::NSPlanning::ITaskPlanner::smoothAndTimeParameterizeECEF(const std::vector<NSDrones::NSCore::EcefPoint,std::allocator<NSDrones::NSCore::EcefPoint>> &,const NSDrones::NSUav::UavPtr &,const NSDrones::NSUav::UavState &,double,NSDrones::NSPlanning::RouteSegment &,NSDrones::NSPlanning::PlanningResult *,const NSDrones::NSMission::ITaskStrategyMap &)”: 不能将“this”指针从“const NSDrones::NSPlanning::SurveyMultiPointsTaskPlanner”转换为“NSDrones::NSPlanning::ITaskPlanner &”
E:\source\dronesplanning\src\planning\task_planners\surveymultipoints_taskplanner.cpp(169,10): message : 转换丢失限定符
E:\source\dronesplanning\include\planning/itask_planner.h(254,9): message : 参见“NSDrones::NSPlanning::ITaskPlanner::smoothAndTimeParameterizeECEF”的声明
E:\source\dronesplanning\src\planning\task_planners\surveymultipoints_taskplanner.cpp(169,10): message : 尝试匹配参数列表“(std::vector<NSDrones::NSCore::EcefPoint,std::allocator<NSDrones::NSCore::EcefPoint>>, const NSDrones::NSUav::UavPtr, NSDrones::NSUav::UavState, double, NSDrones::NSPlanning::RouteSegment, NSDrones::NSPlanning::PlanningResult *, const NSDrones::NSMission::ITaskStrategyMap)”时
  surveysphere_taskplanner.cpp
  正在生成代码...
