// include/mission/task_params.h
#pragma once

#include "core/types.h"           
#include "mission/control_point.h"
#include <vector>
#include <memory>                   
#include <string>
#include <optional>                 

namespace NSDrones {
	namespace NSMission {

		/**
		 * @struct BaseTaskParams
		 * @brief 所有任务特定参数结构体的基类。
		 *        包含一些可能通用的任务参数。
		 */
		struct ITaskParams {
			virtual ~ITaskParams() = default; // 虚析构函数确保正确销毁派生类
		};
		// 智能指针别名
		using ITaskParamsPtr = std::shared_ptr<ITaskParams>;
		using ConstITaskParamsPtr = std::shared_ptr<const ITaskParams>;


		// --- 具体任务参数结构体定义 ---
		// **每个结构体只包含该任务类型特有的数据**
		// 通用参数（如速度、高度、编队）通过 Task 类和 Strategy 管理

		/** @brief LoiterPoint 任务参数 */
		struct LoiterPointTaskParams : ITaskParams {
			ControlPoint center_point; // 盘旋中心控制点
			double radius = 0.0;       // 盘旋半径 (米)
			Time duration_seconds = 0.0; // 盘旋持续时间 (秒)
			bool clockwise = true;      // 盘旋方向 (可选)

			// 构造函数
			LoiterPointTaskParams(ControlPoint center, double r, Time duration, bool cw = true)
				: center_point(std::move(center)), radius(r), duration_seconds(duration), clockwise(cw) {}
		};

		/** @brief FollowPath 任务参数 */
		struct FollowPathTaskParams : ITaskParams {
			ControlPointList waypoints; // 要访问的路径点列表
			int repeat_count = 1;       // 重复次数 (默认为1，实现 GoToPoint 行为)
			bool reverse_on_repeat = true; // 重复时是否反转路径 (默认为是，实现 PatrolLine 行为)

			FollowPathTaskParams(ControlPointList pts, int repeat = 1, bool reverse = true)
				: waypoints(std::move(pts)), repeat_count(std::max(1, repeat)), reverse_on_repeat(reverse) {}
		};

		/** @brief ScanArea 任务参数 */
		struct ScanAreaTaskParams : ITaskParams {
			/** @brief 扫描区域的边界顶点列表 (WGS84坐标，定义扫描平面，至少3个点) */
			std::vector<WGS84Point> area_boundary;
			/** @brief 扫描条带宽度 (米) */
			double strip_width = 10.0;
			/** @brief 扫描条带之间的重叠率 (0.0 到 1.0 之间) */
			double overlap_ratio = 0.1;
			/** @brief 扫描线相对于 X 轴正方向的角度 (度) */
			double scan_angle_deg = 0.0;
			/** @brief (可选) 沿平面法线方向的扫描高度 (米)。默认为 0，即在定义的平面上扫描。 */
			std::optional<double> scan_height_above_plane = 0.0; // 默认在平面上

			// 构造函数
			ScanAreaTaskParams(std::vector<WGS84Point> boundary, double width, double overlap, double angle, std::optional<double> height = 0.0)
				: area_boundary(std::move(boundary)), strip_width(width), overlap_ratio(overlap), scan_angle_deg(angle), scan_height_above_plane(height) {}
		};

		/** @brief SurveySphere 任务参数 */
		struct SurveySphereTaskParams : ITaskParams {
			ControlPoint center_point;       // 球心控制点 (定义球心位置和高度)
			double radius = 0.0;             // 勘察半径 (米)
			std::vector<AnglePair> photo_angles; // 拍摄点的角度列表 (方位角, 俯仰角)

			// 构造函数
			SurveySphereTaskParams(ControlPoint center, double r, std::vector<AnglePair> angles)
				: center_point(std::move(center)), radius(r), photo_angles(std::move(angles)) {}
		};

		/** @brief SurveyCylinder 任务参数 */
		struct SurveyCylinderTaskParams : ITaskParams {
			ControlPoint center_bottom; // 底面中心控制点 (定义底面位置和高度)
			double radius = 0.0;        // 圆柱半径 (米)
			double height = 0.0;        // 圆柱高度 (米)
			double line_spacing = 0.0;  // 扫描线行间距 (米)
			std::optional<double> start_altitude = std::nullopt; // (可选) 起始扫描绝对高度
			bool clockwise = true;      // 扫描方向 (可选)

			// 构造函数
			SurveyCylinderTaskParams(ControlPoint bottom, double r, double h, double spacing,
				std::optional<double> start_alt = std::nullopt, bool cw = true)
				: center_bottom(std::move(bottom)), radius(r), height(h), line_spacing(spacing), start_altitude(start_alt), clockwise(cw) {}
		};

		/** @brief SurveyMultiPoints 任务参数 */
		struct SurveyMultiPointsTaskParams : ITaskParams {
			ControlPointList survey_points; // 勘察点列表 (包含位置、高度、停留时间等)

			// 构造函数
			explicit SurveyMultiPointsTaskParams(ControlPointList points)
				: survey_points(std::move(points)) {}
		};

	} // namespace NSMission
} // namespace NSDrones