// include/mission/task_params.h
#pragma once

#include "core/types.h"           
#include "mission/control_point.h"
#include <vector>
#include <memory>                   
#include <string>
#include <optional>                 

namespace NSDrones {
	namespace NSMission {

		/**
		 * @class ITaskParams
		 * @brief 任务参数的抽象基类
		 *
		 * 定义了所有任务特定参数结构体的基础接口。每种任务类型都有
		 * 其特定的参数结构体继承此基类，用于存储该任务类型独有的
		 * 配置参数和数据。
		 */
		class ITaskParams {
		public:
			virtual ~ITaskParams() = default;

			/**
			 * @brief 验证参数的有效性
			 * @return true表示参数有效，false表示参数无效
			 */
			virtual bool isValid() const = 0;

			/**
			 * @brief 获取参数的描述信息
			 * @return 包含参数详细信息的描述字符串
			 */
			virtual std::string getDescription() const = 0;
		};
		// 智能指针别名
		using ITaskParamsPtr = std::shared_ptr<ITaskParams>;
		using ConstITaskParamsPtr = std::shared_ptr<const ITaskParams>;


		// --- 具体任务参数结构体定义 ---
		// **每个结构体只包含该任务类型特有的数据**
		// 通用参数（如速度、高度、编队）通过 Task 类和 Strategy 管理

		/**
		 * @class LoiterPointTaskParams
		 * @brief 盘旋点任务参数
		 *
		 * 定义无人机在指定点进行盘旋飞行的参数，包括盘旋中心、
		 * 半径、持续时间和方向等。
		 */
		class LoiterPointTaskParams : public ITaskParams {
		public:
			ControlPoint center_point;    ///< 盘旋中心控制点
			double radius;                ///< 盘旋半径（米）
			Time duration_seconds;        ///< 盘旋持续时间（秒）
			bool clockwise;               ///< 盘旋方向（true为顺时针）

			/**
			 * @brief 构造盘旋点任务参数
			 * @param center 盘旋中心控制点
			 * @param r 盘旋半径（米），必须为正数
			 * @param duration 盘旋持续时间（秒），必须为正数
			 * @param cw 盘旋方向，true为顺时针
			 */
			LoiterPointTaskParams(const ControlPoint& center, double r, Time duration, bool cw = true)
				: center_point(center)
				, radius(std::max(0.0, r))
				, duration_seconds(std::max(0.0, duration))
				, clockwise(cw) {}

			// --- 接口实现 ---
			bool isValid() const override {
				return radius > 0.0 && radius <= 1000.0 &&  // 合理的盘旋半径范围
					   duration_seconds > 0.0 && duration_seconds <= 3600.0;  // 最长1小时
			}

			std::string getDescription() const override {
				return "盘旋任务 - 半径: " + std::to_string(radius) + "m, " +
					   "持续时间: " + std::to_string(duration_seconds) + "s, " +
					   "方向: " + (clockwise ? "顺时针" : "逆时针");
			}
		};

		/** @brief FollowPath 任务参数 */
		struct FollowPathTaskParams : ITaskParams {
			ControlPointList waypoints; // 要访问的路径点列表
			int repeat_count = 1;       // 重复次数 (默认为1，实现 GoToPoint 行为)
			bool reverse_on_repeat = true; // 重复时是否反转路径 (默认为是，实现 PatrolLine 行为)

			FollowPathTaskParams(ControlPointList pts, int repeat = 1, bool reverse = true)
				: waypoints(std::move(pts)), repeat_count(std::max(1, repeat)), reverse_on_repeat(reverse) {}
		};

		/** @brief ScanArea 任务参数 */
		struct ScanAreaTaskParams : ITaskParams {
			/** @brief 扫描区域的边界顶点列表 (WGS84坐标，定义扫描平面，至少3个点) */
			std::vector<WGS84Point> area_boundary;
			/** @brief 扫描条带宽度 (米) */
			double strip_width = 10.0;
			/** @brief 扫描条带之间的重叠率 (0.0 到 1.0 之间) */
			double overlap_ratio = 0.1;
			/** @brief 扫描线相对于 X 轴正方向的角度 (度) */
			double scan_angle_deg = 0.0;
			/** @brief (可选) 沿平面法线方向的扫描高度 (米)。默认为 0，即在定义的平面上扫描。 */
			std::optional<double> scan_height_above_plane = 0.0; // 默认在平面上

			// 构造函数
			ScanAreaTaskParams(std::vector<WGS84Point> boundary, double width, double overlap, double angle, std::optional<double> height = 0.0)
				: area_boundary(std::move(boundary)), strip_width(width), overlap_ratio(overlap), scan_angle_deg(angle), scan_height_above_plane(height) {}
		};

		/** @brief SurveySphere 任务参数 */
		struct SurveySphereTaskParams : ITaskParams {
			ControlPoint center_point;       // 球心控制点 (定义球心位置和高度)
			double radius = 0.0;             // 勘察半径 (米)
			std::vector<AnglePair> photo_angles; // 拍摄点的角度列表 (方位角, 俯仰角)

			// 构造函数
			SurveySphereTaskParams(ControlPoint center, double r, std::vector<AnglePair> angles)
				: center_point(std::move(center)), radius(r), photo_angles(std::move(angles)) {}
		};

		/** @brief SurveyCylinder 任务参数 */
		struct SurveyCylinderTaskParams : ITaskParams {
			ControlPoint center_bottom; // 底面中心控制点 (定义底面位置和高度)
			double radius = 0.0;        // 圆柱半径 (米)
			double height = 0.0;        // 圆柱高度 (米)
			double line_spacing = 0.0;  // 扫描线行间距 (米)
			std::optional<double> start_altitude = std::nullopt; // (可选) 起始扫描绝对高度
			bool clockwise = true;      // 扫描方向 (可选)

			// 构造函数
			SurveyCylinderTaskParams(ControlPoint bottom, double r, double h, double spacing,
				std::optional<double> start_alt = std::nullopt, bool cw = true)
				: center_bottom(std::move(bottom)), radius(r), height(h), line_spacing(spacing), start_altitude(start_alt), clockwise(cw) {}
		};

		/** @brief SurveyMultiPoints 任务参数 */
		struct SurveyMultiPointsTaskParams : ITaskParams {
			ControlPointList survey_points; // 勘察点列表 (包含位置、高度、停留时间等)

			// 构造函数
			explicit SurveyMultiPointsTaskParams(ControlPointList points)
				: survey_points(std::move(points)) {}
		};

	} // namespace NSMission
} // namespace NSDrones