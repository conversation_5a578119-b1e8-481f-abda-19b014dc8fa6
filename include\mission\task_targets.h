// include/mission/task_targets.h
#pragma once

#include "core/types.h"
#include "core/geometry/ishape.h"
#include "utils/object_id.h"
#include "utils/logging.h"
#include <vector>
#include <string>
#include <variant>
#include <memory>
#include <sstream>
#include <iomanip>
#include <algorithm>
#include <cmath>
#include <functional>

namespace NSDrones {
	namespace NSMission {
		using namespace NSDrones::NSUtils;
		using namespace NSDrones::NSCore;

		// --- 形状类型别名 ---
		using IShapePtr = std::shared_ptr<IShape>;
		using ConstIShapePtr = std::shared_ptr<const IShape>;

		// --- 任务目标基类 ---
		/**
		 * @class ITaskTarget
		 * @brief 任务目标对象的抽象基类
		 *
		 * 定义了所有任务目标必须实现的基本接口。任务目标可以是点、线、
		 * 区域、体积或特定对象等不同类型，用于指导无人机执行相应的任务。
		 */
		class ITaskTarget {
		public:
			/**
			 * @enum TargetType
			 * @brief 目标类型枚举
			 */
			enum class TargetType {
				POINT,    ///< 点目标（单个位置点）
				LINE,     ///< 线目标（路径或轨迹）
				AREA,     ///< 区域目标（多边形区域）
				VOLUME,   ///< 体积目标（三维空间）
				OBJECT,   ///< 特定对象目标（通过ID引用）
				UNKNOWN   ///< 未知类型
			};

			virtual ~ITaskTarget() = default;

			/**
			 * @brief 获取目标类型
			 * @return 目标的具体类型
			 */
			virtual TargetType getType() const = 0;

			/**
			 * @brief 获取目标的描述信息
			 * @return 包含目标详细信息的描述字符串
			 */
			virtual std::string getDescription() const = 0;

			/**
			 * @brief 验证目标数据的有效性
			 * @return true表示目标数据有效，false表示数据无效
			 */
			virtual bool isValid() const = 0;

			/**
			 * @brief 获取目标的边界框（用于空间查询）
			 * @return 包含目标的最小边界框（第一个点为最小值，第二个点为最大值）
			 */
			virtual std::pair<WGS84Point, WGS84Point> getBoundingBox() const = 0;

			/**
			 * @brief 获取目标的中心点
			 * @return 目标的几何中心点
			 */
			virtual WGS84Point getCenterPoint() const = 0;

			/**
			 * @brief 计算目标的特征尺寸（如半径、长度、面积等）
			 * @return 目标的特征尺寸值，单位为米或平方米
			 */
			virtual double getCharacteristicSize() const = 0;

			/**
			 * @brief 检查指定点是否在目标范围内
			 * @param point 待检查的点
			 * @param tolerance 容差值（米）
			 * @return true表示点在目标范围内
			 */
			virtual bool containsPoint(const WGS84Point& point, double tolerance = 0.0) const = 0;
		};
		using ITaskTargetPtr = std::shared_ptr<ITaskTarget>;
		using ConstITaskTargetPtr = std::shared_ptr<const ITaskTarget>;


		// --- 具体目标类型 ---

		/**
		 * @class PointTarget
		 * @brief 点目标类
		 *
		 * 表示单个地理位置点的任务目标，通常用于定点拍照、
		 * 悬停观察、精确投放等任务。
		 */
		class PointTarget : public ITaskTarget {
		public:
			/**
			 * @brief 构造点目标
			 * @param position 目标位置（WGS84坐标）
			 */
			explicit PointTarget(const WGS84Point& position) : position_(position) {}

			// --- 接口实现 ---
			TargetType getType() const override { return TargetType::POINT; }

			std::string getDescription() const override {
				std::stringstream ss;
				ss << "点目标 (" << std::fixed << std::setprecision(6)
				   << position_.latitude << "°, " << position_.longitude << "°, "
				   << std::setprecision(1) << position_.altitude << "m)";
				return ss.str();
			}

			bool isValid() const override {
				return position_.latitude >= -90.0 && position_.latitude <= 90.0 &&
					   position_.longitude >= -180.0 && position_.longitude <= 180.0;
			}

			std::pair<WGS84Point, WGS84Point> getBoundingBox() const override {
				return {position_, position_}; // 点的边界框就是自身
			}

			WGS84Point getCenterPoint() const override {
				return position_;
			}

			double getCharacteristicSize() const override {
				return 0.0; // 点目标的特征尺寸为0
			}

			bool containsPoint(const WGS84Point& point, double tolerance = 0.0) const override {
				// 使用简单的欧几里得距离计算（应该调用GeometryManager的方法）
				double dx = (point.longitude - position_.longitude) * 111000.0 * std::cos(position_.latitude * M_PI / 180.0);
				double dy = (point.latitude - position_.latitude) * 111000.0;
				double dz = point.altitude - position_.altitude;
				double distance = std::sqrt(dx*dx + dy*dy + dz*dz);
				return distance <= tolerance;
			}

			// --- 访问器 ---
			const WGS84Point& getPosition() const { return position_; }

			/**
			 * @brief 设置新的位置
			 * @param new_position 新的目标位置
			 */
			void setPosition(const WGS84Point& new_position) {
				position_ = new_position;
				LOG_DEBUG("点目标位置已更新: ({:.6f}, {:.6f}, {:.1f})",
						 position_.latitude, position_.longitude, position_.altitude);
			}

		private:
			WGS84Point position_;  ///< 目标位置
		};

		/**
		 * @class LineTarget
		 * @brief 线目标类
		 *
		 * 表示由多个连续点组成的线性目标，通常用于航线巡检、
		 * 管道检测、道路监控等任务。
		 */
		class LineTarget : public ITaskTarget {
		public:
			/**
			 * @brief 构造线目标
			 * @param points 构成线段的有序点列表
			 */
			explicit LineTarget(std::vector<WGS84Point> points) : points_(std::move(points)) {}

			// --- 接口实现 ---
			TargetType getType() const override { return TargetType::LINE; }

			std::string getDescription() const override {
				std::stringstream ss;
				ss << "线目标 (" << points_.size() << " 个点, "
				   << std::fixed << std::setprecision(1) << getTotalLength() << "m)";
				return ss.str();
			}

			bool isValid() const override {
				if (points_.size() < 2) return false;
				for (const auto& point : points_) {
					if (point.latitude < -90.0 || point.latitude > 90.0 ||
						point.longitude < -180.0 || point.longitude > 180.0) {
						return false;
					}
				}
				return true;
			}

			std::pair<WGS84Point, WGS84Point> getBoundingBox() const override {
				if (points_.empty()) {
					return {WGS84Point{}, WGS84Point{}};
				}

				double min_lat = points_[0].latitude, max_lat = points_[0].latitude;
				double min_lon = points_[0].longitude, max_lon = points_[0].longitude;
				double min_alt = points_[0].altitude, max_alt = points_[0].altitude;

				for (const auto& point : points_) {
					min_lat = std::min(min_lat, point.latitude);
					max_lat = std::max(max_lat, point.latitude);
					min_lon = std::min(min_lon, point.longitude);
					max_lon = std::max(max_lon, point.longitude);
					min_alt = std::min(min_alt, point.altitude);
					max_alt = std::max(max_alt, point.altitude);
				}

				return {{min_lat, min_lon, min_alt}, {max_lat, max_lon, max_alt}};
			}

			WGS84Point getCenterPoint() const override {
				if (points_.empty()) {
					return WGS84Point{0.0, 0.0, 0.0};
				}

				// 计算几何中心点
				double sum_lat = 0.0, sum_lon = 0.0, sum_alt = 0.0;
				for (const auto& point : points_) {
					sum_lat += point.latitude;
					sum_lon += point.longitude;
					sum_alt += point.altitude;
				}

				return WGS84Point{
					sum_lat / points_.size(),
					sum_lon / points_.size(),
					sum_alt / points_.size()
				};
			}

			double getCharacteristicSize() const override {
				return getTotalLength();
			}

			bool containsPoint(const WGS84Point& point, double tolerance = 0.0) const override {
				if (points_.size() < 2) return false;

				// 检查点是否在线段的容差范围内
				for (size_t i = 1; i < points_.size(); ++i) {
					if (isPointNearLineSegment(point, points_[i-1], points_[i], tolerance)) {
						return true;
					}
				}
				return false;
			}

			// --- 访问器 ---
			const std::vector<WGS84Point>& getPoints() const { return points_; }

			/**
			 * @brief 获取线段总长度
			 * @return 线段总长度（米）
			 */
			double getTotalLength() const {
				if (points_.size() < 2) return 0.0;

				double total_length = 0.0;
				for (size_t i = 1; i < points_.size(); ++i) {
					// 这里应该调用GeometryManager的距离计算方法
					// total_length += GeometryManager::calculateDistance(points_[i-1], points_[i]);
					// 暂时使用简单的欧几里得距离估算
					double dx = points_[i].longitude - points_[i-1].longitude;
					double dy = points_[i].latitude - points_[i-1].latitude;
					total_length += std::sqrt(dx*dx + dy*dy) * 111000.0; // 粗略转换为米
				}
				return total_length;
			}

			/**
			 * @brief 获取指定索引处的线段
			 * @param index 线段索引
			 * @return 线段的起点和终点
			 */
			std::pair<WGS84Point, WGS84Point> getSegment(size_t index) const {
				if (index >= points_.size() - 1) {
					throw std::out_of_range("线段索引超出范围");
				}
				return {points_[index], points_[index + 1]};
			}

			/**
			 * @brief 获取线段数量
			 * @return 线段数量
			 */
			size_t getSegmentCount() const {
				return points_.size() > 1 ? points_.size() - 1 : 0;
			}

		private:
			std::vector<WGS84Point> points_;  ///< 构成线段的有序点列表

			/**
			 * @brief 检查点是否在线段附近
			 * @param point 待检查的点
			 * @param seg_start 线段起点
			 * @param seg_end 线段终点
			 * @param tolerance 容差值（米）
			 * @return true表示点在线段附近
			 */
			bool isPointNearLineSegment(const WGS84Point& point,
									   const WGS84Point& seg_start,
									   const WGS84Point& seg_end,
									   double tolerance) const {
				// 简化的点到线段距离计算（应该调用GeometryManager的方法）
				// 这里使用简单的投影计算
				double dx = seg_end.longitude - seg_start.longitude;
				double dy = seg_end.latitude - seg_start.latitude;
				double length_sq = dx*dx + dy*dy;

				if (length_sq < 1e-12) {
					// 线段退化为点
					double dist_x = (point.longitude - seg_start.longitude) * 111000.0;
					double dist_y = (point.latitude - seg_start.latitude) * 111000.0;
					return std::sqrt(dist_x*dist_x + dist_y*dist_y) <= tolerance;
				}

				// 计算投影参数
				double t = ((point.longitude - seg_start.longitude) * dx +
						   (point.latitude - seg_start.latitude) * dy) / length_sq;
				t = std::max(0.0, std::min(1.0, t));

				// 计算投影点
				double proj_lon = seg_start.longitude + t * dx;
				double proj_lat = seg_start.latitude + t * dy;

				// 计算距离
				double dist_x = (point.longitude - proj_lon) * 111000.0;
				double dist_y = (point.latitude - proj_lat) * 111000.0;
				double distance = std::sqrt(dist_x*dist_x + dist_y*dist_y);

				return distance <= tolerance;
			}
		};

		/**
		 * @class AreaTarget
		 * @brief 区域目标类
		 *
		 * 表示由边界点定义的多边形区域目标，通常用于区域扫描、
		 * 搜索救援、环境监测等任务。
		 */
		class AreaTarget : public ITaskTarget {
		public:
			/**
			 * @brief 构造区域目标
			 * @param boundary 定义区域边界的有序点列表（多边形顶点）
			 */
			explicit AreaTarget(std::vector<WGS84Point> boundary) : boundary_(std::move(boundary)) {}

			// --- 接口实现 ---
			TargetType getType() const override { return TargetType::AREA; }

			std::string getDescription() const override {
				return "区域目标 (" + std::to_string(boundary_.size()) + " 个顶点, " +
					   std::to_string(getArea()) + "m²)";
			}

			bool isValid() const override {
				if (boundary_.size() < 3) return false; // 至少需要3个点构成多边形
				for (const auto& point : boundary_) {
					if (point.latitude < -90.0 || point.latitude > 90.0 ||
						point.longitude < -180.0 || point.longitude > 180.0) {
						return false;
					}
				}
				return true;
			}

			std::pair<WGS84Point, WGS84Point> getBoundingBox() const override {
				if (boundary_.empty()) {
					return {WGS84Point{}, WGS84Point{}};
				}

				double min_lat = boundary_[0].latitude, max_lat = boundary_[0].latitude;
				double min_lon = boundary_[0].longitude, max_lon = boundary_[0].longitude;
				double min_alt = boundary_[0].altitude, max_alt = boundary_[0].altitude;

				for (const auto& point : boundary_) {
					min_lat = std::min(min_lat, point.latitude);
					max_lat = std::max(max_lat, point.latitude);
					min_lon = std::min(min_lon, point.longitude);
					max_lon = std::max(max_lon, point.longitude);
					min_alt = std::min(min_alt, point.altitude);
					max_alt = std::max(max_alt, point.altitude);
				}

				return {{min_lat, min_lon, min_alt}, {max_lat, max_lon, max_alt}};
			}

			WGS84Point getCenterPoint() const override {
				if (boundary_.empty()) {
					return WGS84Point{0.0, 0.0, 0.0};
				}

				// 计算多边形的重心
				double sum_lat = 0.0, sum_lon = 0.0, sum_alt = 0.0;
				for (const auto& point : boundary_) {
					sum_lat += point.latitude;
					sum_lon += point.longitude;
					sum_alt += point.altitude;
				}

				return WGS84Point{
					sum_lat / boundary_.size(),
					sum_lon / boundary_.size(),
					sum_alt / boundary_.size()
				};
			}

			double getCharacteristicSize() const override {
				return getArea();
			}

			bool containsPoint(const WGS84Point& point, double tolerance = 0.0) const override {
				if (boundary_.size() < 3) return false;

				// 使用射线法判断点是否在多边形内
				// 这是一个简化的实现，应该调用GeometryManager的方法
				bool inside = false;
				size_t j = boundary_.size() - 1;

				for (size_t i = 0; i < boundary_.size(); j = i++) {
					if (((boundary_[i].latitude > point.latitude) != (boundary_[j].latitude > point.latitude)) &&
						(point.longitude < (boundary_[j].longitude - boundary_[i].longitude) *
						(point.latitude - boundary_[i].latitude) / (boundary_[j].latitude - boundary_[i].latitude) +
						boundary_[i].longitude)) {
						inside = !inside;
					}
				}

				return inside;
			}

			// --- 访问器 ---
			const std::vector<WGS84Point>& getBoundary() const { return boundary_; }

			/**
			 * @brief 获取区域面积（粗略估算）
			 * @return 区域面积（平方米）
			 */
			double getArea() const {
				if (boundary_.size() < 3) return 0.0;

				// 使用简单的多边形面积计算（应该调用GeometryManager的方法）
				// 这里只是一个占位符实现
				auto bbox = getBoundingBox();
				double width = (bbox.second.longitude - bbox.first.longitude) * 111000.0;
				double height = (bbox.second.latitude - bbox.first.latitude) * 111000.0;
				return width * height * 0.5; // 粗略估算
			}

			/**
			 * @brief 获取多边形的周长
			 * @return 周长（米）
			 */
			double getPerimeter() const {
				if (boundary_.size() < 2) return 0.0;

				double perimeter = 0.0;
				for (size_t i = 1; i < boundary_.size(); ++i) {
					// 简化的距离计算
					double dx = (boundary_[i].longitude - boundary_[i-1].longitude) * 111000.0;
					double dy = (boundary_[i].latitude - boundary_[i-1].latitude) * 111000.0;
					perimeter += std::sqrt(dx*dx + dy*dy);
				}

				// 闭合多边形
				if (boundary_.size() > 2) {
					double dx = (boundary_[0].longitude - boundary_.back().longitude) * 111000.0;
					double dy = (boundary_[0].latitude - boundary_.back().latitude) * 111000.0;
					perimeter += std::sqrt(dx*dx + dy*dy);
				}

				return perimeter;
			}

		private:
			std::vector<WGS84Point> boundary_;  ///< 定义区域边界的有序点列表
		};

		/**
		 * @class VolumeTarget
		 * @brief 体积目标类
		 *
		 * 表示三维空间中的体积目标，通过几何形状、中心位置和方向定义。
		 * 通常用于建筑物检测、三维重建、复杂结构巡检等任务。
		 */
		class VolumeTarget : public ITaskTarget {
		public:
			/**
			 * @brief 构造体积目标
			 * @param shape 几何形状定义
			 * @param center 中心位置
			 * @param orientation 空间方向
			 */
			VolumeTarget(IShapePtr shape, const WGS84Point& center, const Orientation& orientation)
				: shape_(shape), center_(center), orientation_(orientation) {}

			// --- 接口实现 ---
			TargetType getType() const override { return TargetType::VOLUME; }

			std::string getDescription() const override {
				return "体积目标 (形状: " + (shape_ ? shape_->toString() : "未定义") +
					   ", 中心: " + center_.toString() + ")";
			}

			bool isValid() const override {
				return shape_ != nullptr &&
					   center_.latitude >= -90.0 && center_.latitude <= 90.0 &&
					   center_.longitude >= -180.0 && center_.longitude <= 180.0;
			}

			std::pair<WGS84Point, WGS84Point> getBoundingBox() const override {
				if (!shape_) {
					return {center_, center_};
				}
				// 这里应该根据形状计算实际的边界框
				// 暂时返回中心点作为边界框
				return {center_, center_};
			}

			WGS84Point getCenterPoint() const override {
				return center_;
			}

			double getCharacteristicSize() const override {
				if (!shape_) return 0.0;
				// 这里应该调用shape的体积或特征尺寸方法
				// 暂时返回一个默认值
				return 1000.0; // 占位符
			}

			bool containsPoint(const WGS84Point& point, double tolerance = 0.0) const override {
				if (!shape_) return false;
				// 这里应该调用shape的包含检测方法
				// 暂时使用简单的距离检测
				double dx = (point.longitude - center_.longitude) * 111000.0;
				double dy = (point.latitude - center_.latitude) * 111000.0;
				double dz = point.altitude - center_.altitude;
				double distance = std::sqrt(dx*dx + dy*dy + dz*dz);
				return distance <= (tolerance + 100.0); // 假设100米的默认半径
			}

			// --- 访问器 ---
			std::shared_ptr<const IShape> getShape() const { return shape_; }
			const WGS84Point& getCenter() const { return center_; }
			const Orientation& getOrientation() const { return orientation_; }

			/**
			 * @brief 设置新的中心位置
			 * @param new_center 新的中心位置
			 */
			void setCenter(const WGS84Point& new_center) {
				center_ = new_center;
				LOG_DEBUG("体积目标中心位置已更新: ({:.6f}, {:.6f}, {:.1f})",
						 center_.latitude, center_.longitude, center_.altitude);
			}

			/**
			 * @brief 设置新的方向
			 * @param new_orientation 新的空间方向
			 */
			void setOrientation(const Orientation& new_orientation) {
				orientation_ = new_orientation;
				LOG_DEBUG("体积目标方向已更新");
			}

		private:
			IShapePtr shape_;           ///< 几何形状定义
			WGS84Point center_;         ///< 中心位置
			Orientation orientation_;   ///< 空间方向
		};

		/**
		 * @class ObjectTarget
		 * @brief 特定对象目标类
		 *
		 * 表示通过唯一标识符引用的特定对象目标，如特定的建筑物、
		 * 车辆、人员等。通常用于目标跟踪、定点监控等任务。
		 */
		class ObjectTarget : public ITaskTarget {
		public:
			/**
			 * @brief 构造对象目标
			 * @param target_id 目标对象的唯一标识符
			 */
			explicit ObjectTarget(const ObjectID& target_id) : target_id_(target_id) {}

			// --- 接口实现 ---
			TargetType getType() const override { return TargetType::OBJECT; }

			std::string getDescription() const override {
				return "特定对象目标 (ID: " + target_id_ + ")";
			}

			bool isValid() const override {
				return !target_id_.empty() && NSUtils::isValidObjectID(target_id_);
			}

			std::pair<WGS84Point, WGS84Point> getBoundingBox() const override {
				// 对象目标的边界框需要通过ID查询获得，这里返回默认值
				WGS84Point default_point{0.0, 0.0, 0.0};
				return {default_point, default_point};
			}

			WGS84Point getCenterPoint() const override {
				// 对象目标的中心点需要通过ID查询获得，这里返回默认值
				return WGS84Point{0.0, 0.0, 0.0};
			}

			double getCharacteristicSize() const override {
				// 对象目标的特征尺寸需要通过ID查询获得，这里返回默认值
				return 0.0;
			}

			bool containsPoint(const WGS84Point& point, double tolerance = 0.0) const override {
				// 对象目标的包含检测需要通过ID查询对象的实际几何信息
				// 这里返回false作为默认值
				return false;
			}

			// --- 访问器 ---
			const ObjectID& getTargetId() const { return target_id_; }

			/**
			 * @brief 检查目标ID是否有效
			 * @return true表示ID格式有效
			 */
			bool hasValidId() const {
				return !target_id_.empty() && NSUtils::isValidObjectID(target_id_);
			}

		private:
			ObjectID target_id_;  ///< 目标对象的唯一标识符
		};

		// --- 任务目标 Variant ---
		/**
		 * @brief 任务目标变体类型
		 *
		 * 使用 std::variant 存储不同类型的目标对象指针，允许在运行时
		 * 确定具体的目标类型。这种设计提供了类型安全的多态性，
		 * 避免了传统虚函数的性能开销。
		 */
		using TaskTargetVariant = std::variant<
			std::shared_ptr<PointTarget>,   ///< 点目标指针
			std::shared_ptr<LineTarget>,    ///< 线目标指针
			std::shared_ptr<AreaTarget>,    ///< 区域目标指针
			std::shared_ptr<VolumeTarget>,  ///< 体积目标指针
			std::shared_ptr<ObjectTarget>   ///< 对象目标指针
		>;

		/**
		 * @brief 获取目标变体的类型信息
		 * @param target 目标变体
		 * @return 目标类型枚举值
		 */
		inline ITaskTarget::TargetType getTargetType(const TaskTargetVariant& target) {
			return std::visit([](const auto& ptr) -> ITaskTarget::TargetType {
				return ptr ? ptr->getType() : ITaskTarget::TargetType::UNKNOWN;
			}, target);
		}

		/**
		 * @brief 获取目标变体的描述信息
		 * @param target 目标变体
		 * @return 目标描述字符串
		 */
		inline std::string getTargetDescription(const TaskTargetVariant& target) {
			return std::visit([](const auto& ptr) -> std::string {
				return ptr ? ptr->getDescription() : "无效目标";
			}, target);
		}

		/**
		 * @brief 验证目标变体的有效性
		 * @param target 目标变体
		 * @return true表示目标有效
		 */
		inline bool isTargetValid(const TaskTargetVariant& target) {
			return std::visit([](const auto& ptr) -> bool {
				return ptr && ptr->isValid();
			}, target);
		}

		/**
		 * @brief 获取目标变体的中心点
		 * @param target 目标变体
		 * @return 目标的中心点
		 */
		inline WGS84Point getTargetCenter(const TaskTargetVariant& target) {
			return std::visit([](const auto& ptr) -> WGS84Point {
				return ptr ? ptr->getCenterPoint() : WGS84Point{0.0, 0.0, 0.0};
			}, target);
		}

		/**
		 * @brief 获取目标变体的特征尺寸
		 * @param target 目标变体
		 * @return 目标的特征尺寸
		 */
		inline double getTargetSize(const TaskTargetVariant& target) {
			return std::visit([](const auto& ptr) -> double {
				return ptr ? ptr->getCharacteristicSize() : 0.0;
			}, target);
		}

		/**
		 * @brief 检查点是否在目标范围内
		 * @param target 目标变体
		 * @param point 待检查的点
		 * @param tolerance 容差值（米）
		 * @return true表示点在目标范围内
		 */
		inline bool targetContainsPoint(const TaskTargetVariant& target,
										const WGS84Point& point,
										double tolerance = 0.0) {
			return std::visit([&point, tolerance](const auto& ptr) -> bool {
				return ptr && ptr->containsPoint(point, tolerance);
			}, target);
		}

		// --- 目标工厂方法 ---
		/**
		 * @brief 创建点目标
		 * @param position 目标位置
		 * @return 点目标的智能指针
		 */
		inline std::shared_ptr<PointTarget> createPointTarget(const WGS84Point& position) {
			return std::make_shared<PointTarget>(position);
		}

		/**
		 * @brief 创建线目标
		 * @param points 构成线段的点列表
		 * @return 线目标的智能指针
		 */
		inline std::shared_ptr<LineTarget> createLineTarget(std::vector<WGS84Point> points) {
			return std::make_shared<LineTarget>(std::move(points));
		}

		/**
		 * @brief 创建区域目标
		 * @param boundary 区域边界点列表
		 * @return 区域目标的智能指针
		 */
		inline std::shared_ptr<AreaTarget> createAreaTarget(std::vector<WGS84Point> boundary) {
			return std::make_shared<AreaTarget>(std::move(boundary));
		}

		/**
		 * @brief 创建体积目标
		 * @param shape 几何形状
		 * @param center 中心位置
		 * @param orientation 空间方向
		 * @return 体积目标的智能指针
		 */
		inline std::shared_ptr<VolumeTarget> createVolumeTarget(IShapePtr shape,
																const WGS84Point& center,
																const Orientation& orientation) {
			return std::make_shared<VolumeTarget>(shape, center, orientation);
		}

		/**
		 * @brief 创建对象目标
		 * @param target_id 目标对象ID
		 * @return 对象目标的智能指针
		 */
		inline std::shared_ptr<ObjectTarget> createObjectTarget(const ObjectID& target_id) {
			return std::make_shared<ObjectTarget>(target_id);
		}

		/**
		 * @brief 从目标变体创建TaskTargetVariant
		 * @tparam T 目标类型
		 * @param target 目标对象的智能指针
		 * @return TaskTargetVariant
		 */
		template<typename T>
		inline TaskTargetVariant createTargetVariant(std::shared_ptr<T> target) {
			static_assert(std::is_base_of_v<ITaskTarget, T>, "T must be derived from ITaskTarget");
			return TaskTargetVariant{target};
		}

		// --- 目标变换和操作方法 ---
		/**
		 * @brief 计算两个目标之间的距离
		 * @param target1 第一个目标
		 * @param target2 第二个目标
		 * @return 两个目标中心点之间的距离（米）
		 */
		inline double calculateTargetDistance(const TaskTargetVariant& target1,
											  const TaskTargetVariant& target2) {
			WGS84Point center1 = getTargetCenter(target1);
			WGS84Point center2 = getTargetCenter(target2);

			// 简化的距离计算（应该调用GeometryManager的方法）
			double dx = (center2.longitude - center1.longitude) * 111000.0 *
						std::cos(center1.latitude * M_PI / 180.0);
			double dy = (center2.latitude - center1.latitude) * 111000.0;
			double dz = center2.altitude - center1.altitude;

			return std::sqrt(dx*dx + dy*dy + dz*dz);
		}

		/**
		 * @brief 检查两个目标是否重叠
		 * @param target1 第一个目标
		 * @param target2 第二个目标
		 * @param tolerance 容差值（米）
		 * @return true表示目标重叠
		 */
		inline bool targetsOverlap(const TaskTargetVariant& target1,
								   const TaskTargetVariant& target2,
								   double tolerance = 0.0) {
			WGS84Point center1 = getTargetCenter(target1);
			WGS84Point center2 = getTargetCenter(target2);
			double size1 = getTargetSize(target1);
			double size2 = getTargetSize(target2);

			double distance = calculateTargetDistance(target1, target2);
			double combined_size = (size1 + size2) / 2.0 + tolerance;

			return distance <= combined_size;
		}

		/**
		 * @brief 获取目标的边界框
		 * @param target 目标变体
		 * @return 边界框（最小点，最大点）
		 */
		inline std::pair<WGS84Point, WGS84Point> getTargetBoundingBox(const TaskTargetVariant& target) {
			return std::visit([](const auto& ptr) -> std::pair<WGS84Point, WGS84Point> {
				return ptr ? ptr->getBoundingBox() : std::make_pair(WGS84Point{}, WGS84Point{});
			}, target);
		}

		/**
		 * @brief 目标类型转换为字符串
		 * @param type 目标类型
		 * @return 类型的字符串表示
		 */
		inline std::string targetTypeToString(ITaskTarget::TargetType type) {
			switch (type) {
				case ITaskTarget::TargetType::POINT:  return "点目标";
				case ITaskTarget::TargetType::LINE:   return "线目标";
				case ITaskTarget::TargetType::AREA:   return "区域目标";
				case ITaskTarget::TargetType::VOLUME: return "体积目标";
				case ITaskTarget::TargetType::OBJECT: return "对象目标";
				case ITaskTarget::TargetType::UNKNOWN:
				default: return "未知目标";
			}
		}

		/**
		 * @brief 从字符串解析目标类型
		 * @param type_str 类型字符串
		 * @return 目标类型枚举值
		 */
		inline ITaskTarget::TargetType stringToTargetType(const std::string& type_str) {
			if (type_str == "点目标" || type_str == "POINT") return ITaskTarget::TargetType::POINT;
			if (type_str == "线目标" || type_str == "LINE") return ITaskTarget::TargetType::LINE;
			if (type_str == "区域目标" || type_str == "AREA") return ITaskTarget::TargetType::AREA;
			if (type_str == "体积目标" || type_str == "VOLUME") return ITaskTarget::TargetType::VOLUME;
			if (type_str == "对象目标" || type_str == "OBJECT") return ITaskTarget::TargetType::OBJECT;
			return ITaskTarget::TargetType::UNKNOWN;
		}

		/**
		 * @brief 任务目标系统总结
		 *
		 * 本文件定义了完整的任务目标系统，包括：
		 *
		 * 1. **基础接口 (ITaskTarget)**：
		 *    - 定义了所有目标类型的通用接口
		 *    - 提供类型识别、验证、几何查询等基本功能
		 *
		 * 2. **具体目标类型**：
		 *    - PointTarget：点目标，用于精确定位任务
		 *    - LineTarget：线目标，用于路径跟踪和巡检任务
		 *    - AreaTarget：区域目标，用于区域扫描和覆盖任务
		 *    - VolumeTarget：体积目标，用于三维空间任务
		 *    - ObjectTarget：对象目标，用于特定对象的跟踪和监控
		 *
		 * 3. **变体类型 (TaskTargetVariant)**：
		 *    - 使用std::variant实现类型安全的多态性
		 *    - 避免虚函数调用的性能开销
		 *
		 * 4. **工具函数**：
		 *    - 目标创建工厂方法
		 *    - 目标查询和操作函数
		 *    - 类型转换和验证函数
		 *
		 * 5. **设计特点**：
		 *    - 类型安全：编译时类型检查
		 *    - 高性能：避免虚函数开销
		 *    - 可扩展：易于添加新的目标类型
		 *    - 易用性：提供丰富的工具函数
		 *
		 * @note 几何计算部分使用了简化实现，实际项目中应该调用
		 *       GeometryManager中的精确计算方法。
		 */

	} // namespace NSMission
} // namespace NSDrones