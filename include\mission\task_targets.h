// include/mission/task_targets.h
#pragma once

#include "core/types.h"
#include "core/geometry/ishape.h"  // 新形状系统
#include <vector>
#include <string>
#include <variant>
#include <memory> 
#include <sstream> 
#include <iomanip> 
#include "utils/object_id.h"

namespace NSDrones {
	namespace NSMission {
		using namespace NSDrones::NSUtils;
		using namespace NSDrones::NSCore;

		// --- 形状类型别名 ---
		using IShapePtr = std::shared_ptr<IShape>;
		using ConstIShapePtr = std::shared_ptr<const IShape>;

		// --- 任务目标基类 ---
		/**
		 * @class ITaskTarget
		 * @brief 任务目标对象的抽象基类
		 *
		 * 定义了所有任务目标必须实现的基本接口。任务目标可以是点、线、
		 * 区域、体积或特定对象等不同类型，用于指导无人机执行相应的任务。
		 */
		class ITaskTarget {
		public:
			/**
			 * @enum TargetType
			 * @brief 目标类型枚举
			 */
			enum class TargetType {
				POINT,    ///< 点目标（单个位置点）
				LINE,     ///< 线目标（路径或轨迹）
				AREA,     ///< 区域目标（多边形区域）
				VOLUME,   ///< 体积目标（三维空间）
				OBJECT,   ///< 特定对象目标（通过ID引用）
				UNKNOWN   ///< 未知类型
			};

			virtual ~ITaskTarget() = default;

			/**
			 * @brief 获取目标类型
			 * @return 目标的具体类型
			 */
			virtual TargetType getType() const = 0;

			/**
			 * @brief 获取目标的描述信息
			 * @return 包含目标详细信息的描述字符串
			 */
			virtual std::string getDescription() const = 0;

			/**
			 * @brief 验证目标数据的有效性
			 * @return true表示目标数据有效，false表示数据无效
			 */
			virtual bool isValid() const = 0;

			/**
			 * @brief 获取目标的边界框（用于空间查询）
			 * @return 包含目标的最小边界框
			 */
			virtual std::pair<WGS84Point, WGS84Point> getBoundingBox() const = 0;
		};
		using ITaskTargetPtr = std::shared_ptr<ITaskTarget>;
		using ConstITaskTargetPtr = std::shared_ptr<const ITaskTarget>;


		// --- 具体目标类型 ---

		/**
		 * @class PointTarget
		 * @brief 点目标类
		 *
		 * 表示单个地理位置点的任务目标，通常用于定点拍照、
		 * 悬停观察、精确投放等任务。
		 */
		class PointTarget : public ITaskTarget {
		public:
			/**
			 * @brief 构造点目标
			 * @param position 目标位置（WGS84坐标）
			 */
			explicit PointTarget(const WGS84Point& position) : position_(position) {}

			// --- 接口实现 ---
			TargetType getType() const override { return TargetType::POINT; }

			std::string getDescription() const override {
				std::stringstream ss;
				ss << "点目标 (" << std::fixed << std::setprecision(6)
				   << position_.latitude << "°, " << position_.longitude << "°, "
				   << std::setprecision(1) << position_.altitude << "m)";
				return ss.str();
			}

			bool isValid() const override {
				return position_.latitude >= -90.0 && position_.latitude <= 90.0 &&
					   position_.longitude >= -180.0 && position_.longitude <= 180.0;
			}

			std::pair<WGS84Point, WGS84Point> getBoundingBox() const override {
				return {position_, position_}; // 点的边界框就是自身
			}

			// --- 访问器 ---
			const WGS84Point& getPosition() const { return position_; }

		private:
			WGS84Point position_;  ///< 目标位置
		};

		/**
		 * @class LineTarget
		 * @brief 线目标类
		 *
		 * 表示由多个连续点组成的线性目标，通常用于航线巡检、
		 * 管道检测、道路监控等任务。
		 */
		class LineTarget : public ITaskTarget {
		public:
			/**
			 * @brief 构造线目标
			 * @param points 构成线段的有序点列表
			 */
			explicit LineTarget(std::vector<WGS84Point> points) : points_(std::move(points)) {}

			// --- 接口实现 ---
			TargetType getType() const override { return TargetType::LINE; }

			std::string getDescription() const override {
				return "线目标 (" + std::to_string(points_.size()) + " 个点, " +
					   std::to_string(getTotalLength()) + "m)";
			}

			bool isValid() const override {
				if (points_.size() < 2) return false;
				for (const auto& point : points_) {
					if (point.latitude < -90.0 || point.latitude > 90.0 ||
						point.longitude < -180.0 || point.longitude > 180.0) {
						return false;
					}
				}
				return true;
			}

			std::pair<WGS84Point, WGS84Point> getBoundingBox() const override {
				if (points_.empty()) {
					return {WGS84Point{}, WGS84Point{}};
				}

				double min_lat = points_[0].latitude, max_lat = points_[0].latitude;
				double min_lon = points_[0].longitude, max_lon = points_[0].longitude;
				double min_alt = points_[0].altitude, max_alt = points_[0].altitude;

				for (const auto& point : points_) {
					min_lat = std::min(min_lat, point.latitude);
					max_lat = std::max(max_lat, point.latitude);
					min_lon = std::min(min_lon, point.longitude);
					max_lon = std::max(max_lon, point.longitude);
					min_alt = std::min(min_alt, point.altitude);
					max_alt = std::max(max_alt, point.altitude);
				}

				return {{min_lat, min_lon, min_alt}, {max_lat, max_lon, max_alt}};
			}

			// --- 访问器 ---
			const std::vector<WGS84Point>& getPoints() const { return points_; }

			/**
			 * @brief 获取线段总长度
			 * @return 线段总长度（米）
			 */
			double getTotalLength() const {
				if (points_.size() < 2) return 0.0;

				double total_length = 0.0;
				for (size_t i = 1; i < points_.size(); ++i) {
					// 这里应该调用GeometryManager的距离计算方法
					// total_length += GeometryManager::calculateDistance(points_[i-1], points_[i]);
					// 暂时使用简单的欧几里得距离估算
					double dx = points_[i].longitude - points_[i-1].longitude;
					double dy = points_[i].latitude - points_[i-1].latitude;
					total_length += std::sqrt(dx*dx + dy*dy) * 111000.0; // 粗略转换为米
				}
				return total_length;
			}

		private:
			std::vector<WGS84Point> points_;  ///< 构成线段的有序点列表
		};

		/**
		 * @class AreaTarget
		 * @brief 区域目标类
		 *
		 * 表示由边界点定义的多边形区域目标，通常用于区域扫描、
		 * 搜索救援、环境监测等任务。
		 */
		class AreaTarget : public ITaskTarget {
		public:
			/**
			 * @brief 构造区域目标
			 * @param boundary 定义区域边界的有序点列表（多边形顶点）
			 */
			explicit AreaTarget(std::vector<WGS84Point> boundary) : boundary_(std::move(boundary)) {}

			// --- 接口实现 ---
			TargetType getType() const override { return TargetType::AREA; }

			std::string getDescription() const override {
				return "区域目标 (" + std::to_string(boundary_.size()) + " 个顶点, " +
					   std::to_string(getArea()) + "m²)";
			}

			bool isValid() const override {
				if (boundary_.size() < 3) return false; // 至少需要3个点构成多边形
				for (const auto& point : boundary_) {
					if (point.latitude < -90.0 || point.latitude > 90.0 ||
						point.longitude < -180.0 || point.longitude > 180.0) {
						return false;
					}
				}
				return true;
			}

			std::pair<WGS84Point, WGS84Point> getBoundingBox() const override {
				if (boundary_.empty()) {
					return {WGS84Point{}, WGS84Point{}};
				}

				double min_lat = boundary_[0].latitude, max_lat = boundary_[0].latitude;
				double min_lon = boundary_[0].longitude, max_lon = boundary_[0].longitude;
				double min_alt = boundary_[0].altitude, max_alt = boundary_[0].altitude;

				for (const auto& point : boundary_) {
					min_lat = std::min(min_lat, point.latitude);
					max_lat = std::max(max_lat, point.latitude);
					min_lon = std::min(min_lon, point.longitude);
					max_lon = std::max(max_lon, point.longitude);
					min_alt = std::min(min_alt, point.altitude);
					max_alt = std::max(max_alt, point.altitude);
				}

				return {{min_lat, min_lon, min_alt}, {max_lat, max_lon, max_alt}};
			}

			// --- 访问器 ---
			const std::vector<WGS84Point>& getBoundary() const { return boundary_; }

			/**
			 * @brief 获取区域面积（粗略估算）
			 * @return 区域面积（平方米）
			 */
			double getArea() const {
				if (boundary_.size() < 3) return 0.0;

				// 使用简单的多边形面积计算（应该调用GeometryManager的方法）
				// 这里只是一个占位符实现
				auto bbox = getBoundingBox();
				double width = (bbox.second.longitude - bbox.first.longitude) * 111000.0;
				double height = (bbox.second.latitude - bbox.first.latitude) * 111000.0;
				return width * height * 0.5; // 粗略估算
			}

		private:
			std::vector<WGS84Point> boundary_;  ///< 定义区域边界的有序点列表
		};

		/**
		 * @class VolumeTarget
		 * @brief 体积目标类
		 *
		 * 表示三维空间中的体积目标，通过几何形状、中心位置和方向定义。
		 * 通常用于建筑物检测、三维重建、复杂结构巡检等任务。
		 */
		class VolumeTarget : public ITaskTarget {
		public:
			/**
			 * @brief 构造体积目标
			 * @param shape 几何形状定义
			 * @param center 中心位置
			 * @param orientation 空间方向
			 */
			VolumeTarget(IShapePtr shape, const WGS84Point& center, const Orientation& orientation)
				: shape_(shape), center_(center), orientation_(orientation) {}

			// --- 接口实现 ---
			TargetType getType() const override { return TargetType::VOLUME; }

			std::string getDescription() const override {
				return "体积目标 (形状: " + (shape_ ? shape_->toString() : "未定义") +
					   ", 中心: " + center_.toString() + ")";
			}

			bool isValid() const override {
				return shape_ != nullptr &&
					   center_.latitude >= -90.0 && center_.latitude <= 90.0 &&
					   center_.longitude >= -180.0 && center_.longitude <= 180.0;
			}

			std::pair<WGS84Point, WGS84Point> getBoundingBox() const override {
				if (!shape_) {
					return {center_, center_};
				}
				// 这里应该根据形状计算实际的边界框
				// 暂时返回中心点作为边界框
				return {center_, center_};
			}

			// --- 访问器 ---
			std::shared_ptr<const IShape> getShape() const { return shape_; }
			const WGS84Point& getCenter() const { return center_; }
			const Orientation& getOrientation() const { return orientation_; }

		private:
			IShapePtr shape_;           ///< 几何形状定义
			WGS84Point center_;         ///< 中心位置
			Orientation orientation_;   ///< 空间方向
		};

		/**
		 * @class ObjectTarget
		 * @brief 特定对象目标类
		 *
		 * 表示通过唯一标识符引用的特定对象目标，如特定的建筑物、
		 * 车辆、人员等。通常用于目标跟踪、定点监控等任务。
		 */
		class ObjectTarget : public ITaskTarget {
		public:
			/**
			 * @brief 构造对象目标
			 * @param target_id 目标对象的唯一标识符
			 */
			explicit ObjectTarget(const ObjectID& target_id) : target_id_(target_id) {}

			// --- 接口实现 ---
			TargetType getType() const override { return TargetType::OBJECT; }

			std::string getDescription() const override {
				return "特定对象目标 (ID: " + target_id_ + ")";
			}

			bool isValid() const override {
				return !target_id_.empty() && NSUtils::isValidObjectID(target_id_);
			}

			std::pair<WGS84Point, WGS84Point> getBoundingBox() const override {
				// 对象目标的边界框需要通过ID查询获得，这里返回默认值
				WGS84Point default_point{0.0, 0.0, 0.0};
				return {default_point, default_point};
			}

			// --- 访问器 ---
			const ObjectID& getTargetId() const { return target_id_; }

		private:
			ObjectID target_id_;  ///< 目标对象的唯一标识符
		};

		// --- 任务目标 Variant ---
		/**
		 * @brief 任务目标变体类型
		 *
		 * 使用 std::variant 存储不同类型的目标对象指针，允许在运行时
		 * 确定具体的目标类型。这种设计提供了类型安全的多态性，
		 * 避免了传统虚函数的性能开销。
		 */
		using TaskTargetVariant = std::variant<
			std::shared_ptr<PointTarget>,   ///< 点目标指针
			std::shared_ptr<LineTarget>,    ///< 线目标指针
			std::shared_ptr<AreaTarget>,    ///< 区域目标指针
			std::shared_ptr<VolumeTarget>,  ///< 体积目标指针
			std::shared_ptr<ObjectTarget>   ///< 对象目标指针
		>;

		/**
		 * @brief 获取目标变体的类型信息
		 * @param target 目标变体
		 * @return 目标类型枚举值
		 */
		inline ITaskTarget::TargetType getTargetType(const TaskTargetVariant& target) {
			return std::visit([](const auto& ptr) -> ITaskTarget::TargetType {
				return ptr ? ptr->getType() : ITaskTarget::TargetType::UNKNOWN;
			}, target);
		}

		/**
		 * @brief 获取目标变体的描述信息
		 * @param target 目标变体
		 * @return 目标描述字符串
		 */
		inline std::string getTargetDescription(const TaskTargetVariant& target) {
			return std::visit([](const auto& ptr) -> std::string {
				return ptr ? ptr->getDescription() : "无效目标";
			}, target);
		}
	} // namespace NSMission
} // namespace NSDrones